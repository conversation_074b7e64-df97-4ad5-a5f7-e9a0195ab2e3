# 📊 Panduan Import Database ABSENSI SIGAP dengan Tabel Patroli

## ✅ **Database Sudah Lengkap!**

File `DB/absensi_sigap.sql` sudah berisi **semua tabel yang diperlukan** termasuk:
- ✅ Tabel `tbl_patroli` (tabel patroli utama)
- ✅ Tabel `tbl_checklist` (data checklist untuk patroli)
- ✅ Tabel `tbl_lokasi` (data lokasi patroli)
- ✅ Semua tabel sistem absensi yang sudah ada
- ✅ Data sample untuk testing
- ✅ Foreign key relationships
- ✅ Auto increment settings

## 🚀 **Cara Import Database**

### **Metode 1: Via phpMyAdmin (Recommended)**

1. **Buka phpMyAdmin**
   ```
   http://localhost/phpmyadmin
   ```

2. **Buat Database Baru (jika belum ada)**
   - Klik "New" di sidebar kiri
   - Nama database: `absensi_sigap`
   - Collation: `utf8mb4_general_ci`
   - <PERSON><PERSON> "Create"

3. **Import File SQL**
   - Pilih database `absensi_sigap`
   - <PERSON><PERSON> tab "Import"
   - Klik "Choose File"
   - Pilih file: `DB/absensi_sigap.sql`
   - Klik "Go"

4. **Verifikasi Import**
   - Cek apakah semua tabel ter-create
   - Total tabel yang harus ada: 12 tabel

### **Metode 2: Via MySQL Command Line**

```bash
# Masuk ke MySQL
mysql -u root -p

# Buat database (jika belum ada)
CREATE DATABASE absensi_sigap;
USE absensi_sigap;

# Import file
SOURCE C:/xampp7.4/htdocs/SC ABSENSI SIGAP/DB/absensi_sigap.sql;

# Atau via command line langsung
mysql -u root -p absensi_sigap < "C:/xampp7.4/htdocs/SC ABSENSI SIGAP/DB/absensi_sigap.sql"
```

### **Metode 3: Copy-Paste Manual**

1. Buka file `DB/absensi_sigap.sql` dengan text editor
2. Copy semua isi file
3. Buka phpMyAdmin → pilih database → tab "SQL"
4. Paste code dan klik "Go"

## 📋 **Verifikasi Setelah Import**

### **1. Cek Semua Tabel**
```sql
SHOW TABLES;
```

**Hasil yang diharapkan:**
```
+-------------------------+
| Tables_in_absensi_sigap |
+-------------------------+
| building                |
| cuty                    |
| employees               |
| holiday                 |
| permission              |
| position                |
| presence                |
| present_status          |
| shift                   |
| sw_site                 |
| tbl_checklist          |
| tbl_lokasi             |
| tbl_patroli            |
| user                   |
| user_level             |
+-------------------------+
```

### **2. Cek Struktur Tabel Patroli**
```sql
DESCRIBE tbl_patroli;
```

### **3. Cek Data Sample**
```sql
-- Cek data patroli
SELECT p.id_patroli, e.employees_name, l.nama_lokasi, c.nama_pekerjaan, p.status, p.rating
FROM tbl_patroli p
LEFT JOIN employees e ON p.id_karyawan = e.id
LEFT JOIN tbl_lokasi l ON p.id_lokasi = l.id_lokasi
LEFT JOIN tbl_checklist c ON p.id_ceklis = c.id_checklist;

-- Cek data checklist
SELECT * FROM tbl_checklist;

-- Cek data lokasi
SELECT * FROM tbl_lokasi;
```

### **4. Test Foreign Key Relationships**
```sql
-- Test insert data baru (harus berhasil)
INSERT INTO tbl_patroli (status, rating, id_karyawan, id_lokasi, id_ceklis, komentar) 
VALUES ('Selesai', 4, 36, 1, 1, 'Test patroli baru');

-- Cek data baru
SELECT * FROM tbl_patroli ORDER BY id_patroli DESC LIMIT 1;
```

## 📊 **Data Sample yang Tersedia**

### **Karyawan (employees)**
- ID 36: Ikmal Hanaan Zikri
- ID 37: Saddan Husein  
- ID 38: Taufik
- ID 39: NANDA
- ID 40: Greata
- ID 41: Setiawan

### **Lokasi (tbl_lokasi)**
- ID 1: titik 1 (cek titik 1)

### **Checklist (tbl_checklist)**
- ID 1: Pemeriksaan Keamanan
- ID 2: Pemeriksaan Kebersihan
- ID 3: Pemeriksaan Fasilitas

### **Patroli Sample (tbl_patroli)**
- 4 data sample patroli dengan berbagai status
- Menggunakan karyawan dan lokasi yang ada
- Rating 3-5 bintang

## ⚠️ **Troubleshooting**

### **Error: Table already exists**
```sql
-- Hapus tabel yang conflict
DROP TABLE IF EXISTS tbl_patroli;
DROP TABLE IF EXISTS tbl_checklist;

-- Lalu import ulang
```

### **Error: Foreign key constraint fails**
```sql
-- Cek apakah tabel referensi ada
SELECT * FROM employees WHERE id IN (36, 37, 38, 39);
SELECT * FROM tbl_lokasi WHERE id_lokasi = 1;

-- Jika tidak ada, sesuaikan data sample
```

### **Error: Access denied**
- Pastikan user MySQL memiliki privilege CREATE, INSERT, ALTER
- Login sebagai root atau user dengan privilege penuh

## 🎯 **Setelah Import Berhasil**

### **1. Test Akses Admin Panel**
```
http://localhost/SC ABSENSI SIGAP/sw-admin/
```

### **2. Test Menu Patroli**
- Login sebagai Administrator atau Operator
- Klik menu "Data Patroli"
- Test tambah data patroli baru
- Test star rating functionality

### **3. Test CRUD Operations**
- Create: Tambah patroli baru
- Read: Lihat daftar patroli
- Update: Edit patroli existing
- Delete: Hapus patroli (hanya admin)

## 📞 **Support**

Jika mengalami masalah:
1. Cek error message di phpMyAdmin
2. Pastikan XAMPP MySQL berjalan
3. Cek file log MySQL untuk detail error
4. Backup database sebelum mencoba import ulang

Database siap digunakan! 🎉
