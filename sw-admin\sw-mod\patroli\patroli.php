<?php
if(empty($connection)){
  header('location:../../');
} else {
  include_once 'sw-mod/sw-panel.php';
echo'
  <div class="content-wrapper">';
switch(@$_GET['op']){ 
    default:
echo'
<section class="content-header">
  <h1>Data<small> Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li class="active">Data Patroli</li>
    </ol>
</section>';
echo'
<section class="content">
  <div class="row">
    <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title"><b>Data Patroli</b></h3>
          <div class="box-tools pull-right">';
          if($level_user==1 || $level_user==2){
            echo'
            <a href="'.$mod.'&op=add" class="btn btn-success btn-flat"><i class="fa fa-plus"></i> Tambah Baru</a>';}
          else{
            echo'<button type="button" class="btn btn-success btn-flat access-failed"><i class="fa fa-plus"></i> Tambah Baru</button>';
          }echo'
          </div>
        </div>
    <div class="box-body">
      <div class="table-responsive">
          <table id="sw-datatable" class="table table-bordered">
            <thead>
            <tr>
              <th style="width: 10px">No</th>
              <th>Tanggal</th>
              <th>Karyawan</th>
              <th>Lokasi</th>
              <th>Checklist</th>
              <th>Status</th>
              <th>Rating</th>
              <th>Dokumentasi</th>
              <th style="width:150px" class="text-center">Aksi</th>
            </tr>
            </thead>
            <tbody>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</section>';
break;

case 'add':
echo'
<section class="content-header">
  <h1>Tambah<small> Data Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li><a href="./'.$mod.'">Data Patroli</a></li>
      <li class="active">Tambah</li>
    </ol>
</section>

<section class="content">
  <div class="row">
    <div class="col-md-8">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title">Form Tambah Data Patroli</h3>
        </div>
        <form method="post" action="sw-mod/'.$mod.'/proses.php?action=add" enctype="multipart/form-data">
          <div class="box-body">
            <div class="form-group">
              <label>Karyawan <span class="text-red">*</span></label>
              <select name="id_karyawan" class="form-control" required>
                <option value="">- Pilih Karyawan -</option>';
                $query_karyawan = "SELECT id, employees_name FROM employees ORDER BY employees_name ASC";
                $result_karyawan = $connection->query($query_karyawan);
                if($result_karyawan->num_rows > 0){
                  while($row_karyawan = $result_karyawan->fetch_assoc()){
                    echo '<option value="'.$row_karyawan['id'].'">'.$row_karyawan['employees_name'].'</option>';
                  }
                }
                echo'
              </select>
            </div>
            
            <div class="form-group">
              <label>Lokasi <span class="text-red">*</span></label>
              <select name="id_lokasi" class="form-control" required>
                <option value="">- Pilih Lokasi -</option>';
                $query_lokasi = "SELECT id_lokasi, nama_lokasi FROM tbl_lokasi ORDER BY nama_lokasi ASC";
                $result_lokasi = $connection->query($query_lokasi);
                if($result_lokasi->num_rows > 0){
                  while($row_lokasi = $result_lokasi->fetch_assoc()){
                    echo '<option value="'.$row_lokasi['id_lokasi'].'">'.$row_lokasi['nama_lokasi'].'</option>';
                  }
                }
                echo'
              </select>
            </div>
            
            <div class="form-group">
              <label>Checklist <span class="text-red">*</span></label>
              <select name="id_ceklis" class="form-control" required>
                <option value="">- Pilih Checklist -</option>';
                $query_checklist = "SELECT id_checklist, nama_pekerjaan FROM tbl_checklist ORDER BY nama_pekerjaan ASC";
                $result_checklist = $connection->query($query_checklist);
                if($result_checklist->num_rows > 0){
                  while($row_checklist = $result_checklist->fetch_assoc()){
                    echo '<option value="'.$row_checklist['id_checklist'].'">'.$row_checklist['nama_pekerjaan'].'</option>';
                  }
                }
                echo'
              </select>
            </div>
            
            <div class="form-group">
              <label>Status <span class="text-red">*</span></label>
              <select name="status" class="form-control" required>
                <option value="">- Pilih Status -</option>
                <option value="Dalam Proses">Dalam Proses</option>
                <option value="Selesai">Selesai</option>
                <option value="Tertunda">Tertunda</option>
                <option value="Dibatalkan">Dibatalkan</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>Rating <span class="text-red">*</span></label>
              <div class="star-rating" id="star-rating-container">
                <span class="star" onclick="setRating(1)" onmouseover="hoverRating(1)" onmouseout="resetHover()" data-value="1"><i class="fa fa-star-o" id="star-1"></i></span>
                <span class="star" onclick="setRating(2)" onmouseover="hoverRating(2)" onmouseout="resetHover()" data-value="2"><i class="fa fa-star-o" id="star-2"></i></span>
                <span class="star" onclick="setRating(3)" onmouseover="hoverRating(3)" onmouseout="resetHover()" data-value="3"><i class="fa fa-star-o" id="star-3"></i></span>
                <span class="star" onclick="setRating(4)" onmouseover="hoverRating(4)" onmouseout="resetHover()" data-value="4"><i class="fa fa-star-o" id="star-4"></i></span>
                <span class="star" onclick="setRating(5)" onmouseover="hoverRating(5)" onmouseout="resetHover()" data-value="5"><i class="fa fa-star-o" id="star-5"></i></span>
              </div>
              <input type="hidden" name="rating" id="rating-value" required>
              <small class="text-muted">Klik bintang untuk memberikan rating</small>
              <div id="rating-display" style="margin-top: 5px; font-weight: bold; color: #666;">Rating: <span id="rating-text">Belum dipilih</span></div>
            </div>
            
            <div class="form-group">
              <label>Dokumentasi</label>
              <input type="file" name="dokumentasi" class="form-control" accept="image/*">
              <small class="text-muted">Format: JPG, JPEG, PNG. Maksimal 2MB</small>
            </div>
            
            <div class="form-group">
              <label>Komentar</label>
              <textarea name="komentar" class="form-control" rows="4" placeholder="Masukkan komentar atau catatan..."></textarea>
            </div>
          </div>
          <div class="box-footer">
            <button type="submit" class="btn btn-success"><i class="fa fa-save"></i> Simpan</button>
            <a href="./'.$mod.'" class="btn btn-default"><i class="fa fa-arrow-left"></i> Kembali</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>';
break;

case 'edit':
if(!empty($_GET['id'])){
  $id = mysqli_real_escape_string($connection, $_GET['id']);
  $query = "SELECT p.*, e.employees_name, l.nama_lokasi, c.nama_pekerjaan 
            FROM tbl_patroli p 
            LEFT JOIN employees e ON p.id_karyawan = e.id 
            LEFT JOIN tbl_lokasi l ON p.id_lokasi = l.id_lokasi 
            LEFT JOIN tbl_checklist c ON p.id_ceklis = c.id_checklist 
            WHERE p.id_patroli = '$id'";
  $result = $connection->query($query);
  if($result->num_rows > 0){
    $row = $result->fetch_assoc();
echo'
<section class="content-header">
  <h1>Edit<small> Data Patroli</small></h1>
    <ol class="breadcrumb">
      <li><a href="./"><i class="fa fa-dashboard"></i> Beranda</a></li>
      <li><a href="./'.$mod.'">Data Patroli</a></li>
      <li class="active">Edit</li>
    </ol>
</section>

<section class="content">
  <div class="row">
    <div class="col-md-8">
      <div class="box box-solid">
        <div class="box-header with-border">
          <h3 class="box-title">Form Edit Data Patroli</h3>
        </div>
        <form method="post" action="sw-mod/'.$mod.'/proses.php?action=update" enctype="multipart/form-data">
          <input type="hidden" name="id_patroli" value="'.$row['id_patroli'].'">
          <div class="box-body">
            <div class="form-group">
              <label>Karyawan <span class="text-red">*</span></label>
              <select name="id_karyawan" class="form-control" required>
                <option value="">- Pilih Karyawan -</option>';
                $query_karyawan = "SELECT id, employees_name FROM employees ORDER BY employees_name ASC";
                $result_karyawan = $connection->query($query_karyawan);
                if($result_karyawan->num_rows > 0){
                  while($row_karyawan = $result_karyawan->fetch_assoc()){
                    $selected = ($row_karyawan['id'] == $row['id_karyawan']) ? 'selected' : '';
                    echo '<option value="'.$row_karyawan['id'].'" '.$selected.'>'.$row_karyawan['employees_name'].'</option>';
                  }
                }
                echo'
              </select>
            </div>

            <div class="form-group">
              <label>Lokasi <span class="text-red">*</span></label>
              <select name="id_lokasi" class="form-control" required>
                <option value="">- Pilih Lokasi -</option>';
                $query_lokasi = "SELECT id_lokasi, nama_lokasi FROM tbl_lokasi ORDER BY nama_lokasi ASC";
                $result_lokasi = $connection->query($query_lokasi);
                if($result_lokasi->num_rows > 0){
                  while($row_lokasi = $result_lokasi->fetch_assoc()){
                    $selected = ($row_lokasi['id_lokasi'] == $row['id_lokasi']) ? 'selected' : '';
                    echo '<option value="'.$row_lokasi['id_lokasi'].'" '.$selected.'>'.$row_lokasi['nama_lokasi'].'</option>';
                  }
                }
                echo'
              </select>
            </div>

            <div class="form-group">
              <label>Checklist <span class="text-red">*</span></label>
              <select name="id_ceklis" class="form-control" required>
                <option value="">- Pilih Checklist -</option>';
                $query_checklist = "SELECT id_checklist, nama_pekerjaan FROM tbl_checklist ORDER BY nama_pekerjaan ASC";
                $result_checklist = $connection->query($query_checklist);
                if($result_checklist->num_rows > 0){
                  while($row_checklist = $result_checklist->fetch_assoc()){
                    $selected = ($row_checklist['id_checklist'] == $row['id_ceklis']) ? 'selected' : '';
                    echo '<option value="'.$row_checklist['id_checklist'].'" '.$selected.'>'.$row_checklist['nama_pekerjaan'].'</option>';
                  }
                }
                echo'
              </select>
            </div>

            <div class="form-group">
              <label>Status <span class="text-red">*</span></label>
              <select name="status" class="form-control" required>
                <option value="">- Pilih Status -</option>
                <option value="Dalam Proses" '.($row['status'] == 'Dalam Proses' ? 'selected' : '').'>Dalam Proses</option>
                <option value="Selesai" '.($row['status'] == 'Selesai' ? 'selected' : '').'>Selesai</option>
                <option value="Tertunda" '.($row['status'] == 'Tertunda' ? 'selected' : '').'>Tertunda</option>
                <option value="Dibatalkan" '.($row['status'] == 'Dibatalkan' ? 'selected' : '').'>Dibatalkan</option>
              </select>
            </div>

            <div class="form-group">
              <label>Rating <span class="text-red">*</span></label>
              <div class="star-rating" id="star-rating-container">
                <span class="star" onclick="setRating(1)" onmouseover="hoverRating(1)" onmouseout="resetHover()" data-value="1"><i class="fa fa-star'.($row['rating'] >= 1 ? '' : '-o').'" id="star-1"></i></span>
                <span class="star" onclick="setRating(2)" onmouseover="hoverRating(2)" onmouseout="resetHover()" data-value="2"><i class="fa fa-star'.($row['rating'] >= 2 ? '' : '-o').'" id="star-2"></i></span>
                <span class="star" onclick="setRating(3)" onmouseover="hoverRating(3)" onmouseout="resetHover()" data-value="3"><i class="fa fa-star'.($row['rating'] >= 3 ? '' : '-o').'" id="star-3"></i></span>
                <span class="star" onclick="setRating(4)" onmouseover="hoverRating(4)" onmouseout="resetHover()" data-value="4"><i class="fa fa-star'.($row['rating'] >= 4 ? '' : '-o').'" id="star-4"></i></span>
                <span class="star" onclick="setRating(5)" onmouseover="hoverRating(5)" onmouseout="resetHover()" data-value="5"><i class="fa fa-star'.($row['rating'] >= 5 ? '' : '-o').'" id="star-5"></i></span>
              </div>
              <input type="hidden" name="rating" id="rating-value" value="'.$row['rating'].'" required>
              <small class="text-muted">Klik bintang untuk memberikan rating</small>
              <div id="rating-display" style="margin-top: 5px; font-weight: bold; color: #666;">Rating: <span id="rating-text">'.$row['rating'].' bintang</span></div>
            </div>

            <div class="form-group">
              <label>Dokumentasi</label>';
              if(!empty($row['dokumentasi'])){
                echo '<div class="mb-2">
                  <img src="../sw-content/patroli/'.$row['dokumentasi'].'" alt="Dokumentasi" style="max-width: 200px; max-height: 200px;" class="img-thumbnail">
                  <p><small>File saat ini: '.$row['dokumentasi'].'</small></p>
                </div>';
              }
              echo'
              <input type="file" name="dokumentasi" class="form-control" accept="image/*">
              <small class="text-muted">Format: JPG, JPEG, PNG. Maksimal 2MB. Kosongkan jika tidak ingin mengubah.</small>
            </div>

            <div class="form-group">
              <label>Komentar</label>
              <textarea name="komentar" class="form-control" rows="4" placeholder="Masukkan komentar atau catatan...">'.htmlspecialchars($row['komentar']).'</textarea>
            </div>
          </div>
          <div class="box-footer">
            <button type="submit" class="btn btn-success"><i class="fa fa-save"></i> Update</button>
            <a href="./'.$mod.'" class="btn btn-default"><i class="fa fa-arrow-left"></i> Kembali</a>
          </div>
        </form>
      </div>
    </div>
  </div>
</section>';
  } else {
    echo '<div class="alert alert-danger">Data tidak ditemukan!</div>';
  }
} else {
  echo '<div class="alert alert-danger">ID tidak valid!</div>';
}
break;

}
echo'
  </div>

<script type="text/javascript">
// Simple Star Rating Function
function setRating(rating) {
    console.log("Setting rating to:", rating);

    // Update hidden input
    document.getElementById("rating-value").value = rating;

    // Update rating text
    document.getElementById("rating-text").innerText = rating + " bintang";

    // Update star display
    for (var i = 1; i <= 5; i++) {
        var star = document.getElementById("star-" + i);
        if (star) {
            if (i <= rating) {
                star.className = "fa fa-star";
            } else {
                star.className = "fa fa-star-o";
            }
        }
    }

    // Remove error styling if exists
    var container = document.getElementById("star-rating-container");
    if (container) {
        container.classList.remove("error-rating");
    }

    console.log("Rating set successfully");
}

// Hover effects (optional)
function hoverRating(rating) {
    for (var i = 1; i <= 5; i++) {
        var star = document.getElementById("star-" + i);
        if (star) {
            if (i <= rating) {
                star.style.color = "#f39c12";
            } else {
                star.style.color = "#ddd";
            }
        }
    }
}

function resetHover() {
    var currentRating = document.getElementById("rating-value").value || 0;
    for (var i = 1; i <= 5; i++) {
        var star = document.getElementById("star-" + i);
        if (star) {
            if (i <= currentRating) {
                star.style.color = "#f39c12";
            } else {
                star.style.color = "#ddd";
            }
        }
    }
}
</script>

<style>
.star-rating {
    font-size: 24px;
    margin: 10px 0;
}
.star {
    cursor: pointer;
    margin-right: 5px;
    transition: all 0.2s ease;
    display: inline-block;
}
.star:hover {
    transform: scale(1.1);
}
.star i {
    color: #ddd;
    transition: color 0.2s ease;
}
.star.active i,
.star.hover i {
    color: #f39c12;
}
.star:hover i {
    color: #f39c12;
}
.star-rating .star:last-child {
    margin-right: 0;
}
.error-rating {
    border: 2px dashed #dd4b39;
    border-radius: 5px;
    padding: 5px;
    background-color: rgba(221, 75, 57, 0.1);
}
.error-rating .star i {
    color: #dd4b39 !important;
}
</style>';

// Include JavaScript untuk DataTable dan fungsi lainnya
include_once 'sw-mod/'.$mod.'/scripts.js';
}
?>
