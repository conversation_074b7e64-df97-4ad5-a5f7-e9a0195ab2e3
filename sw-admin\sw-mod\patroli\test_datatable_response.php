<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once '../../../sw-library/sw-config.php';
require_once '../../login/login_session.php';
include('../../../sw-library/sw-function.php');

echo "<h2>🔍 Test DataTable Response Patroli</h2>";
echo "<p>Halaman ini akan menguji response dari sw-datatable.php secara langsung</p>";

// Simulate DataTable request parameters
$_GET['sEcho'] = 1;
$_GET['iDisplayStart'] = 0;
$_GET['iDisplayLength'] = 10;
$_GET['iSortingCols'] = 1;
$_GET['iSortCol_0'] = 1;
$_GET['sSortDir_0'] = 'desc';
$_GET['sSearch'] = '';
$_GET['debug'] = 1;

// Set all bSortable and bSearchable parameters
for($i = 0; $i < 9; $i++) {
    $_GET['bSortable_' . $i] = ($i == 0 || $i == 7 || $i == 8) ? 'false' : 'true';
    $_GET['bSearchable_' . $i] = ($i == 0 || $i == 6 || $i == 7 || $i == 8) ? 'false' : 'true';
}

echo "<h3>1. 📋 Parameter Request</h3>";
echo "<pre>";
print_r($_GET);
echo "</pre>";

echo "<h3>2. 🔄 Test Response dari sw-datatable.php</h3>";

// Capture output from sw-datatable.php
ob_start();
include 'sw-datatable.php';
$response = ob_get_clean();

echo "<h4>Raw Response:</h4>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

echo "<h4>Parsed JSON:</h4>";
$json_data = json_decode($response, true);

if($json_data) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h5>✅ JSON Valid</h5>";
    echo "<p><strong>Total Records:</strong> " . $json_data['iTotalRecords'] . "</p>";
    echo "<p><strong>Filtered Records:</strong> " . $json_data['iTotalDisplayRecords'] . "</p>";
    echo "<p><strong>Data Rows:</strong> " . count($json_data['aaData']) . "</p>";
    
    if(isset($json_data['debug'])) {
        echo "<h5>Debug Info:</h5>";
        echo "<pre>";
        print_r($json_data['debug']);
        echo "</pre>";
    }
    
    if(!empty($json_data['aaData'])) {
        echo "<h5>Sample Data (First 2 rows):</h5>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>No</th><th>Tanggal</th><th>Karyawan</th><th>Lokasi</th><th>Checklist</th><th>Status</th><th>Rating</th><th>Dokumentasi</th><th>Aksi</th>";
        echo "</tr>";
        
        for($i = 0; $i < min(2, count($json_data['aaData'])); $i++) {
            echo "<tr>";
            foreach($json_data['aaData'][$i] as $cell) {
                echo "<td>" . htmlspecialchars(strip_tags($cell)) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background: #28a745; color: white; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>🎉 SUCCESS: Data berhasil diambil dari database!</strong>";
        echo "</div>";
    } else {
        echo "<div style='background: #ffc107; color: black; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>⚠️ WARNING: Response berhasil tapi data kosong</strong>";
        echo "</div>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h5>❌ JSON Invalid</h5>";
    echo "<p><strong>JSON Error:</strong> " . json_last_error_msg() . "</p>";
    echo "<p><strong>Response Length:</strong> " . strlen($response) . " characters</p>";
    echo "</div>";
}

echo "<h3>3. 🔍 Test Database Langsung</h3>";

// Test database directly
$query = "SELECT 
            p.id_patroli,
            p.tanggal,
            e.employees_name,
            l.nama_lokasi,
            c.nama_pekerjaan,
            p.status,
            p.rating,
            p.dokumentasi,
            p.komentar
          FROM tbl_patroli p 
          LEFT JOIN employees e ON p.id_karyawan = e.id 
          LEFT JOIN tbl_lokasi l ON p.id_lokasi = l.id_lokasi 
          LEFT JOIN tbl_checklist c ON p.id_ceklis = c.id_checklist
          ORDER BY p.tanggal DESC 
          LIMIT 5";

$result = $connection->query($query);

if($result) {
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h5>✅ Query Database Berhasil</h5>";
    echo "<p><strong>Rows Found:</strong> " . $result->num_rows . "</p>";
    
    if($result->num_rows > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>ID</th><th>Tanggal</th><th>Karyawan</th><th>Lokasi</th><th>Checklist</th><th>Status</th><th>Rating</th>";
        echo "</tr>";
        
        while($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id_patroli'] . "</td>";
            echo "<td>" . $row['tanggal'] . "</td>";
            echo "<td>" . ($row['employees_name'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['nama_lokasi'] ?: 'NULL') . "</td>";
            echo "<td>" . ($row['nama_pekerjaan'] ?: 'NULL') . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . $row['rating'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background: #28a745; color: white; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>🎉 SUCCESS: Data ada di database dan bisa diambil!</strong>";
        echo "</div>";
    } else {
        echo "<div style='background: #ffc107; color: black; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
        echo "<strong>⚠️ WARNING: Query berhasil tapi tidak ada data</strong>";
        echo "</div>";
    }
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px;'>";
    echo "<h5>❌ Query Database Gagal</h5>";
    echo "<p><strong>Error:</strong> " . $connection->error . "</p>";
    echo "</div>";
}

echo "<h3>4. 💡 Kesimpulan</h3>";
echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px;'>";
echo "<h5>Analisis:</h5>";
echo "<ul>";
echo "<li>Jika JSON valid dan data ada = sw-datatable.php bekerja dengan benar</li>";
echo "<li>Jika database query berhasil tapi JSON kosong = masalah di sw-datatable.php</li>";
echo "<li>Jika database query kosong = masalah di database atau data belum ada</li>";
echo "<li>Jika JSON invalid = masalah syntax atau error di sw-datatable.php</li>";
echo "</ul>";
echo "</div>";

echo "<br><br>";
echo "<a href='../../../patroli' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>← Kembali ke Data Patroli</a> ";
echo "<a href='test_final.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Test Final →</a>";
?>
