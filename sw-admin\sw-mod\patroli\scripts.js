<script type="text/javascript">
$(document).ready(function() {
    // Initialize DataTable
    var table = $('#sw-datatable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "sw-mod/patroli/sw-datatable.php",
            "type": "GET"
        },
        "columns": [
            { "data": 0, "orderable": false, "searchable": false, "width": "50px" }, // No
            { "data": 1, "orderable": true, "searchable": true }, // Tanggal
            { "data": 2, "orderable": true, "searchable": true }, // <PERSON><PERSON><PERSON>
            { "data": 3, "orderable": true, "searchable": true }, // Lokasi
            { "data": 4, "orderable": true, "searchable": true }, // Checklist
            { "data": 5, "orderable": true, "searchable": true }, // Status
            { "data": 6, "orderable": true, "searchable": false }, // Rating
            { "data": 7, "orderable": false, "searchable": false }, // Dokumentasi
            { "data": 8, "orderable": false, "searchable": false, "width": "150px" } // Aksi
        ],
        "order": [[ 1, "desc" ]], // Order by tanggal descending
        "pageLength": 25,
        "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
        "language": {
            "processing": "Memproses...",
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
            "infoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
            "infoFiltered": "(disaring dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        },
        "responsive": true,
        "autoWidth": false
    });

    // Detail Modal
    $(document).on('click', '.btn-detail', function() {
        var id = $(this).data('id');
        var karyawan = $(this).data('karyawan');
        var lokasi = $(this).data('lokasi');
        var checklist = $(this).data('checklist');
        var status = $(this).data('status');
        var rating = $(this).data('rating');
        var tanggal = $(this).data('tanggal');
        var komentar = $(this).data('komentar');
        var dokumentasi = $(this).data('dokumentasi');
        
        var stars = '<div class="rating-display-modal">';
        for(var i = 1; i <= 5; i++){
            if(i <= rating){
                stars += '<i class="fa fa-star" style="color: #f39c12; font-size: 18px; margin-right: 2px;"></i>';
            } else {
                stars += '<i class="fa fa-star-o" style="color: #ddd; font-size: 18px; margin-right: 2px;"></i>';
            }
        }
        stars += '</div>';
        
        var dokumentasiHtml = '';
        if(dokumentasi){
            dokumentasiHtml = '<img src="../sw-content/patroli/' + dokumentasi + '" alt="Dokumentasi" class="img-responsive" style="max-width: 100%; max-height: 300px;">';
        } else {
            dokumentasiHtml = '<p class="text-muted">Tidak ada dokumentasi</p>';
        }
        
        var modalContent = `
            <div class="modal fade" id="detailModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <h4 class="modal-title">Detail Data Patroli</h4>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-bordered">
                                        <tr>
                                            <td width="40%"><strong>Tanggal</strong></td>
                                            <td>${tanggal}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Karyawan</strong></td>
                                            <td>${karyawan}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Lokasi</strong></td>
                                            <td>${lokasi}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Checklist</strong></td>
                                            <td>${checklist}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Status</strong></td>
                                            <td>${status}</td>
                                        </tr>
                                        <tr>
                                            <td><strong>Rating</strong></td>
                                            <td>${stars}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <h5><strong>Dokumentasi:</strong></h5>
                                    ${dokumentasiHtml}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <h5><strong>Komentar:</strong></h5>
                                    <p>${komentar || 'Tidak ada komentar'}</p>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-default" data-dismiss="modal">Tutup</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // Remove existing modal if any
        $('#detailModal').remove();
        
        // Add modal to body and show
        $('body').append(modalContent);
        $('#detailModal').modal('show');
    });

    // Delete confirmation
    $(document).on('click', '.btn-delete', function() {
        var id = $(this).data('id');
        
        if(confirm('Apakah Anda yakin ingin menghapus data patroli ini?')) {
            var form = $('<form method="post" action="sw-mod/patroli/proses.php?action=delete"></form>');
            form.append('<input type="hidden" name="id" value="' + id + '">');
            $('body').append(form);
            form.submit();
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        var errorMessage = '';

        // Check required fields
        $(this).find('input[required], select[required], textarea[required]').each(function() {
            if($(this).val() === '') {
                isValid = false;
                $(this).addClass('error');

                // Special handling for rating field
                if($(this).attr('id') === 'rating-value') {
                    errorMessage += '- Rating harus dipilih\n';
                    $(this).closest('.form-group').find('.star-rating').addClass('error-rating');
                } else {
                    errorMessage += '- ' + $(this).prev('label').text() + ' harus diisi\n';
                }
            } else {
                $(this).removeClass('error');
                if($(this).attr('id') === 'rating-value') {
                    $(this).closest('.form-group').find('.star-rating').removeClass('error-rating');
                }
            }
        });
        
        // Check file upload if exists
        var fileInput = $(this).find('input[type="file"]');
        if(fileInput.length > 0 && fileInput[0].files.length > 0) {
            var file = fileInput[0].files[0];
            var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
            var maxSize = 2 * 1024 * 1024; // 2MB
            
            if(!allowedTypes.includes(file.type)) {
                isValid = false;
                errorMessage += '- Format file harus JPG, JPEG, atau PNG\n';
            }
            
            if(file.size > maxSize) {
                isValid = false;
                errorMessage += '- Ukuran file maksimal 2MB\n';
            }
        }
        
        if(!isValid) {
            e.preventDefault();
            alert('Terdapat kesalahan:\n' + errorMessage);
        }
    });

    // File preview
    $('input[type="file"]').on('change', function() {
        var file = this.files[0];
        if(file) {
            var reader = new FileReader();
            reader.onload = function(e) {
                var preview = '<div class="file-preview mt-2"><img src="' + e.target.result + '" alt="Preview" style="max-width: 200px; max-height: 200px;" class="img-thumbnail"></div>';
                $(this).parent().find('.file-preview').remove();
                $(this).parent().append(preview);
            }.bind(this);
            reader.readAsDataURL(file);
        }
    });

    // Star Rating functionality moved to inline script in patroli.php

    // Access failed notification
    $(document).on('click', '.access-failed', function(e) {
        e.preventDefault();
        alert('Anda tidak memiliki akses untuk melakukan tindakan ini!');
    });
});

// Add custom CSS for error styling (star rating CSS moved to inline)
$('<style>')
    .prop('type', 'text/css')
    .html(`
        .error {
            border-color: #dd4b39 !important;
            box-shadow: 0 0 0 0.2rem rgba(221, 75, 57, 0.25) !important;
        }
        .file-preview {
            margin-top: 10px;
        }
        .btn-group-sm > .btn, .btn-sm {
            margin-right: 2px;
        }
        .rating-display {
            white-space: nowrap;
        }
        .rating-display i {
            margin-right: 1px;
        }
        .rating-display-modal {
            display: inline-block;
        }
        .rating-display-modal i {
            margin-right: 3px;
        }
    `)
    .appendTo('head');
</script>
