{"name": "webcamjs", "version": "1.0.25", "description": "HTML5 Webcam Image Capture Library with Flash Fallback", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/jhuckaby/webcamjs", "license": "MIT", "main": "webcam.js", "scripts": {"build": "./build.sh"}, "repository": {"type": "git", "url": "https://github.com/jhuckaby/webcamjs"}, "bugs": {"url": "https://github.com/jhuckaby/webcamjs/issues"}, "keywords": ["webcam", "camera", "getusermedia", "flash", "jpegcam"], "dependencies": {}, "devDependencies": {}}