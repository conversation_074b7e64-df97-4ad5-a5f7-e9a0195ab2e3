# WebcamJS is in Maintenance Mode

Hey everyone!  WebcamJS v1.x is going into maintenance mode as of Feb 11, 2017.  That means I will only be able to fix critical bugs from here on in.  I will not be adding any new features, or accepting any new PRs into this version.  I am working on an all-new WebcamJS v2.0 implementation, which will feature real-time canvas effects, and plugin drivers for mobile support.

See [DOCS.md](https://github.com/jhuckaby/webcamjs/blob/master/DOCS.md) for the v1.x documentation.

Looking for a good alternative to WebcamJS?  Please check out [JpegCamera](https://github.com/amw/jpeg_camera) by [<PERSON>](https://github.com/amw).  It has many advanced features that WebcamJS is lacking (for example, upload multiple photos at once, retry failed uploads, CSRF tokens, make sure camera is ready), and has a very clean and object-oriented design.

## Troubleshooting

Having trouble?  See if your webcam is actually working in your browser:

- Try the basic demo: https://pixlcore.com/demos/webcamjs/demos/basic.html
- Try this HTML5 Webcam test page: https://simpl.info/getusermedia/
- And this one: https://davidwalsh.name/demo/camera.php
- Try this Flash Webcam test page: https://www.onlinemictest.com/webcam-test-in-adobe-flash
- Try jQuery Webcam: http://www.xarg.org/project/jquery-webcam-plugin/
- Try JpegCamera: https://github.com/amw/jpeg_camera
