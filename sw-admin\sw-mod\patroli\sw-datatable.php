<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once '../../../sw-library/sw-config.php';
require_once '../../login/login_session.php';
include('../../../sw-library/sw-function.php');

// Set content type untuk JSON
header('Content-Type: application/json');

// Error reporting untuk debugging
if(isset($_GET['debug'])) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

$aColumns = [
    'id_patroli',
    'tanggal', 
    'employees_name',
    'nama_lokasi',
    'nama_pekerjaan',
    'status',
    'rating',
    'dokumentasi'
];

$sIndexColumn = "id_patroli";
$sTable = "tbl_patroli p 
           LEFT JOIN employees e ON p.id_karyawan = e.id 
           LEFT JOIN tbl_lokasi l ON p.id_lokasi = l.id_lokasi 
           LEFT JOIN tbl_checklist c ON p.id_ceklis = c.id_checklist";

$gaSql['user'] = DB_USER;
$gaSql['password'] = DB_PASSWD;
$gaSql['db'] = DB_NAME;
$gaSql['server'] = DB_HOST;

$gaSql['link'] = new mysqli($gaSql['server'], $gaSql['user'], $gaSql['password'], $gaSql['db']);

if($gaSql['link']->connect_error){
    die('Connection failed: ' . $gaSql['link']->connect_error);
}

$sLimit = "";
if(isset($_GET['iDisplayStart']) && $_GET['iDisplayLength'] != '-1'){
    $sLimit = "LIMIT " . intval($_GET['iDisplayStart']) . ", " . intval($_GET['iDisplayLength']);
}

$sOrder = "";
if(isset($_GET['iSortCol_0'])){
    $sOrder = "ORDER BY ";
    for($i = 0; $i < intval($_GET['iSortingCols']); $i++){
        if($_GET['bSortable_' . intval($_GET['iSortCol_' . $i])] == "true"){
            if($i > 0) $sOrder .= ", ";
            $sOrder .= $aColumns[intval($_GET['iSortCol_' . $i])] . " " . ($_GET['sSortDir_' . $i] === 'asc' ? 'asc' : 'desc');
        }
    }
    if($sOrder == "ORDER BY"){
        $sOrder = "";
    }
}

$sWhere = "";
if(isset($_GET['sSearch']) && $_GET['sSearch'] != ""){
    $sWhere = "WHERE (";
    for($i = 0; $i < count($aColumns); $i++){
        if(isset($_GET['bSearchable_' . $i]) && $_GET['bSearchable_' . $i] == "true"){
            if($i > 0) $sWhere .= " OR ";
            $sWhere .= $aColumns[$i] . " LIKE '%" . $gaSql['link']->real_escape_string($_GET['sSearch']) . "%'";
        }
    }
    $sWhere .= ")";
}

for($i = 0; $i < count($aColumns); $i++){
    if(isset($_GET['bSearchable_' . $i]) && $_GET['bSearchable_' . $i] == "true" && $_GET['sSearch_' . $i] != ''){
        if($sWhere == ""){
            $sWhere = "WHERE ";
        } else {
            $sWhere .= " AND ";
        }
        $sWhere .= $aColumns[$i] . " LIKE '%" . $gaSql['link']->real_escape_string($_GET['sSearch_' . $i]) . "%'";
    }
}

$sQuery = "SELECT SQL_CALC_FOUND_ROWS 
           p.id_patroli,
           p.tanggal,
           e.employees_name,
           l.nama_lokasi,
           c.nama_pekerjaan,
           p.status,
           p.rating,
           p.dokumentasi,
           p.komentar
           FROM " . $sTable . " " . $sWhere . " " . $sOrder . " " . $sLimit;

$rResult = $gaSql['link']->query($sQuery) or die($gaSql['link']->error);

$sQueryTotal = "SELECT FOUND_ROWS()";
$rResultTotal = $gaSql['link']->query($sQueryTotal) or die($gaSql['link']->error);
$aResultTotal = $rResultTotal->fetch_array();
$iTotal = $aResultTotal[0];

$sQueryFiltered = "SELECT COUNT(*) FROM " . $sTable . " " . $sWhere;
$rResultFiltered = $gaSql['link']->query($sQueryFiltered) or die($gaSql['link']->error);
$aResultFiltered = $rResultFiltered->fetch_array();
$iFilteredTotal = $aResultFiltered[0];

$output = array(
    "sEcho" => intval($_GET['sEcho']),
    "iTotalRecords" => $iTotal,
    "iTotalDisplayRecords" => $iFilteredTotal,
    "aaData" => array()
);

$no = $_GET['iDisplayStart'] + 1;
while($aRow = $rResult->fetch_array()){
    $row = array();
    
    // No
    $row[] = $no++;
    
    // Tanggal
    $row[] = tgl_indo($aRow['tanggal']) . ' ' . jam_indo($aRow['tanggal']);
    
    // Karyawan
    $row[] = $aRow['employees_name'];
    
    // Lokasi
    $row[] = $aRow['nama_lokasi'];
    
    // Checklist
    $row[] = $aRow['nama_pekerjaan'];
    
    // Status
    $status_class = '';
    switch($aRow['status']){
        case 'Selesai':
            $status_class = 'label-success';
            break;
        case 'Dalam Proses':
            $status_class = 'label-warning';
            break;
        case 'Tertunda':
            $status_class = 'label-info';
            break;
        case 'Dibatalkan':
            $status_class = 'label-danger';
            break;
        default:
            $status_class = 'label-default';
    }
    $row[] = '<span class="label ' . $status_class . '">' . $aRow['status'] . '</span>';
    
    // Rating
    $stars = '<div class="rating-display">';
    for($i = 1; $i <= 5; $i++){
        if($i <= $aRow['rating']){
            $stars .= '<i class="fa fa-star" style="color: #f39c12; font-size: 14px;"></i>';
        } else {
            $stars .= '<i class="fa fa-star-o" style="color: #ddd; font-size: 14px;"></i>';
        }
    }
    $stars .= '</div>';
    $row[] = $stars;
    
    // Dokumentasi
    if(!empty($aRow['dokumentasi'])){
        $row[] = '<a href="../sw-content/patroli/' . $aRow['dokumentasi'] . '" target="_blank" class="btn btn-xs btn-info">
                    <i class="fa fa-image"></i> Lihat
                  </a>';
    } else {
        $row[] = '<span class="text-muted">Tidak ada</span>';
    }
    
    // Aksi
    $aksi = '<div class="btn-group btn-group-sm">';
    
    // Detail button
    $aksi .= '<button class="btn btn-info btn-xs btn-detail" 
                data-id="' . $aRow['id_patroli'] . '"
                data-karyawan="' . htmlspecialchars($aRow['employees_name']) . '"
                data-lokasi="' . htmlspecialchars($aRow['nama_lokasi']) . '"
                data-checklist="' . htmlspecialchars($aRow['nama_pekerjaan']) . '"
                data-status="' . htmlspecialchars($aRow['status']) . '"
                data-rating="' . $aRow['rating'] . '"
                data-tanggal="' . tgl_indo($aRow['tanggal']) . ' ' . jam_indo($aRow['tanggal']) . '"
                data-komentar="' . htmlspecialchars($aRow['komentar']) . '"
                data-dokumentasi="' . $aRow['dokumentasi'] . '"
                title="Detail">
                <i class="fa fa-eye"></i>
              </button>';
    
    // Edit button (hanya untuk level 1 dan 2)
    if($level_user == 1 || $level_user == 2){
        $aksi .= '<a href="patroli&op=edit&id=' . $aRow['id_patroli'] . '" class="btn btn-warning btn-xs" title="Edit">
                    <i class="fa fa-pencil"></i>
                  </a>';
    } else {
        $aksi .= '<button class="btn btn-warning btn-xs access-failed" title="Edit">
                    <i class="fa fa-pencil"></i>
                  </button>';
    }
    
    // Delete button (hanya untuk level 1)
    if($level_user == 1){
        $aksi .= '<button class="btn btn-danger btn-xs btn-delete" 
                    data-id="' . epm_encode($aRow['id_patroli']) . '" 
                    title="Hapus">
                    <i class="fa fa-trash"></i>
                  </button>';
    } else {
        $aksi .= '<button class="btn btn-danger btn-xs access-failed" title="Hapus">
                    <i class="fa fa-trash"></i>
                  </button>';
    }
    
    $aksi .= '</div>';
    $row[] = $aksi;
    
    $output['aaData'][] = $row;
}

// Add debug info if requested
if(isset($_GET['debug'])) {
    $output['debug_info'] = array(
        'total_rows_found' => count($output['aaData']),
        'query_executed' => $sQuery,
        'connection_status' => $gaSql['link']->ping() ? 'Connected' : 'Disconnected',
        'user_level' => $level_user,
        'session_user' => $_SESSION['SESSION_USER']
    );
}

// Ensure we have valid JSON
$json_output = json_encode($output);
if($json_output === false) {
    // JSON encoding failed
    $error_output = array(
        "sEcho" => intval($_GET['sEcho']),
        "iTotalRecords" => 0,
        "iTotalDisplayRecords" => 0,
        "aaData" => array(),
        "error" => "JSON encoding failed: " . json_last_error_msg()
    );
    echo json_encode($error_output);
} else {
    echo $json_output;
}
?>
