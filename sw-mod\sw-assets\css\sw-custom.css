:focus {
  outline: 0;
}

::-webkit-scrollbar-track{
  -webkit-box-shadow: inset 0 0 2px rgba(0,0,0,0.3);
  background-color: #F5F5F5;
}

::-webkit-scrollbar{
  width:8px;
  background-color: #F5F5F5;
}

::-webkit-scrollbar-thumb{
  background-color: #000000;
}


.loading {
  display: none;
  position: fixed;
  top:60px;
  right: 20px;
  z-index:999999;
}

.wallet-card .wallet-footer .webcame{
  display:inline-block;
  text-align: center;
  align-items: flex-start;
  justify-content: space-between;
  margin:auto;
}

.wallet-card .wallet-footer .webcame canvas {
    width:600px;
    height:auto;
    border-radius: 10px;
    overflow:hidden;
}


div.dataTables_filter label {
    font-weight: normal;
    white-space: nowrap;
    text-align: left;
    float: right;
}


/* --------------------------------
    Pagination
-----------------------------------*/
div.dataTables_paginate ul.pagination{
  float: right;
}
div.dataTables_info {
    padding-top: 8px;
    padding-left: 10px;
    white-space: nowrap;
}

.pagination>li>a, .pagination>li>span {
  margin: 0 1px;
  color: #333;
  padding:5px 10px;
  line-height:33px;
  font-weight:500;
  font-size:14px;
  background-color: #FFFFFF;
  border:solid 1px #eee;
}

.pagination>li>a:hover{
    color: #ffffff;
    background:#ff396f;
    border-color:#ff396f;
}

.pagination>li:first-child>a, .pagination>li:first-child>span{
    margin-left: 0;
    border-top-left-radius: 20px;
    border-bottom-left-radius:20px;
}

.pagination>li:last-child>a, .pagination>li:last-child>span {
    border-top-right-radius: 20px;
    border-bottom-right-radius:20px;
}

.pagination>.active>a, .pagination>.active>a:focus,
.pagination>.active>a:hover, .pagination>.active>span,
.pagination>.active>span:focus, .pagination>.active>span:hover {
    z-index: 2;
    color: #ffffff;
    cursor: default;
    background:#ff396f;
    border-color:#ff396f;
  
}
.avatar-section {
  position: relative;
}
.avatar-section input[type="file"] {
  display: inline-block;
  opacity: 0;
  position: absolute;
  margin-left: 20px;
  margin-right: 20px;
  padding-top: 30px;
  padding-bottom: 67px;
  width: 85%;
  z-index: 99;
  margin-top: 10px;
  cursor:pointer;
}


/* -----  ID Card -----------*/
.id-card{
  position: relative;
  width:300px;
  height:430px;
  margin:auto;
  border-radius:30px;
  overflow: hidden;
  box-shadow: 0 5px 20px 0 rgb(0 0 0 / 9%);
}

.id-card .body-id-card{
    background:url(../img/bg-id-card.png) no-repeat center;
    height:100%;
    width: 100%;
    padding: 30px;
}

.id-card .body-id-card .avatar{
  margin-bottom: 10px;
}

.id-card .body-id-card h3{
  font-size: 16px;
  text-align: center;
  margin: auto;
}

.id-card .body-id-card p{
  font-size: 14px;
  text-transform: uppercase;
  color: #333333;
  font-weight:600;
}

.id-card .body-id-card .barcode{
  position: relative;
  display: inline-block;
  background:#ffffff;
  height: 155px;
  width: 155px;
  box-shadow: 0 5px 20px 0 rgb(0 0 0 / 9%);
  margin-top: 30px;
  border:solid 2px #ffffff;
  overflow: hidden;
  text-align: center;
}

.id-card .body-id-card .barcode img{
  height:150px;
  width: 150px;
  text-align: center;
  display: inline-block;
}


/* --------- WEBCAME -----------*/
.webcam-capture-body{
  display: inline-block;
  text-align: center;
  margin: auto;
}

.webcam-capture,
.webcam-capture video{
  display: inline-block;
  width:100%!important;
  height: auto!important;
  margin:auto;
  text-align: center;
  border-radius: 15px;
  overflow: hidden;
}


.webcam-capture-body .webcam-camera{
  position: relative;
  background:#ffffff;
  padding:20px;
  border:solid 1px #eeeeee;
  border-radius:20px;
  width:100%;
  height: auto;
}

.webcam-capture-body .webcam-camera img{
  width:100%;
  object-fit: cover;
  background: #eeeeee;
  border: solid 1px #eeeeee;
  border-radius: 10px;
  margin-bottom: 10px;
}


.webcam-capture-body  input[type="file"] {
  display: inline-block;
  opacity:0;
  position: absolute;
  margin-left: 0px;
  margin-right: 0px;
  padding-top:15px;
  padding-left:25px;
  width: 100%;
  height: 60px;
  z-index: 99;
  cursor: pointer;
}




.select {
  cursor: pointer;
  background-color: transparent;
  box-shadow: none;
  border: 0px;
  font-size: 15px;
  font-weight: 500;
}




/*-----------------------
 Upload Image
--------------------------*/

.upload-media{
    background: #ffffff;
    background-size: 100%!important;
    border:solid 1px #eeeeee;
    height:100px;
    width:100px;
    cursor:pointer;
    margin-right: 10px;
    margin-bottom: 10px;
    text-align: center;
    border-radius: 10px;
    position:relative;
    overflow: hidden;
}
 

.upload-media:hover{
    background: #fbfbfb;
    -webkit-box-shadow: 0px 0px 10px 1px rgba(217,217,217,1);
    -moz-box-shadow: 0px 0px 10px 1px rgba(217,217,217,1);
    box-shadow: 0px 0px 10px 1px rgba(217,217,217,1);
}

.upload-media input.upload-hidden{
    width:100%;height:100px;
    position:absolute!important;
    top:0!important;
    left:0!important;
    padding:0; 
    margin-top:0px; 
    margin-left:0px;
    opacity:0!important;
    cursor:pointer;
    display: block !important;
}

.upload-media i{
  font-size: 30px;
  color: #999999;
  line-height:96px;
}
    
.upload-media img {
    vertical-align:middle;
    width:100px;
    height:100px;
    background-size: 100%;
    object-fit: contain;
  }
.upload-media .images{opacity: 0}
.upload-media .images:hover{
    opacity:1
}

.upload-content{
    position: relative;
}

.upload-content .btn.btn-primary .upload-files{
    width:100%;height:100px;
    position:absolute!important;
    top:0!important;
    left:0!important;
    padding:0; 
    margin-top:0px; 
    margin-left:0px;
    opacity:0!important;
    cursor:pointer;
    display: block !important;
}


@media only screen and (max-width:690px){
  .wallet-card .wallet-footer .webcame canvas {
    width: 100%;
    height:auto;
  }
}
@media only screen and (max-width:480px){
  .wallet-card .wallet-footer .webcame canvas {
    width: 100%;
    height:230px;
  }
  
  div.dataTables_filter label {
    float:left;
  }
  div.dataTables_filter input {
    margin-left: 0.5em;
    display: inline-block;
    width: 180px;
  }

  .wallet-card {
    padding:10px 15px;
    position: relative;
    z-index: 1;
  }

  .id-card {
      position: relative;
      width:250px;
      height:auto;
      border-radius: 30px;
  }

  .stat-box .value {
      font-size:15px;
  }

  .hidden-sm{
    display: none;
  }
}

@media print {
  .hidden-print {
    display: none !important;
  }
  a[href]:after {
    content: none !important;
  }

 header, footer, aside, nav, form, iframe, .menu, .hero, .adslot{
    display: none;
  }

}


@page:right{
  @bottom-right {
    content: counter(page);
  }
}

@page:left{
  @bottom-left {
    content: counter(page);
  }
}