<!DOCTYPE html>
<html>
<head>
    <title>Test DataTable Patroli</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../../sw-assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../sw-assets/css/dataTables.bootstrap.min.css">
    <link rel="stylesheet" href="../../sw-assets/css/font-awesome.min.css">
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #e7f3ff; border-color: #b3d9ff; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1>🧪 Test DataTable Patroli Langsung</h1>
        <p>Halaman ini menguji DataTable patroli secara langsung tanpa melalui halaman admin utama.</p>
        
        <div class="test-section info">
            <h3>📋 Test DataTable</h3>
            <p>Tabel di bawah ini menggunakan DataTable yang sama dengan halaman patroli admin:</p>
            
            <table id="test-datatable" class="table table-bordered table-striped">
                <thead>
                    <tr>
                        <th>No</th>
                        <th>Tanggal</th>
                        <th>Karyawan</th>
                        <th>Lokasi</th>
                        <th>Checklist</th>
                        <th>Status</th>
                        <th>Rating</th>
                        <th>Dokumentasi</th>
                        <th>Aksi</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
        
        <div class="test-section">
            <h3>🔍 Debug Information</h3>
            <div id="debug-info">
                <p>Loading debug information...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="test-results">
                <p>Waiting for DataTable to load...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔗 Navigation</h3>
            <a href="../../../patroli" class="btn btn-primary">← Kembali ke Data Patroli</a>
            <a href="test_display.php" class="btn btn-info">Test Display →</a>
            <a href="check_setup.php" class="btn btn-warning">Check Setup →</a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../../sw-assets/js/jquery.min.js"></script>
    <script src="../../sw-assets/js/bootstrap.min.js"></script>
    <script src="../../sw-assets/js/jquery.dataTables.min.js"></script>
    <script src="../../sw-assets/js/dataTables.bootstrap.min.js"></script>
    
    <script>
    $(document).ready(function() {
        console.log('Starting DataTable test...');
        
        // Test 1: Direct AJAX call to sw-datatable.php
        $.ajax({
            url: 'sw-datatable.php?debug=1',
            type: 'GET',
            data: {
                sEcho: 1,
                iDisplayStart: 0,
                iDisplayLength: 10,
                iSortingCols: 1,
                iSortCol_0: 1,
                sSortDir_0: 'desc',
                bSortable_0: 'false',
                bSortable_1: 'true',
                bSortable_2: 'true',
                bSortable_3: 'true',
                bSortable_4: 'true',
                bSortable_5: 'true',
                bSortable_6: 'true',
                bSortable_7: 'false',
                bSortable_8: 'false',
                bSearchable_0: 'false',
                bSearchable_1: 'true',
                bSearchable_2: 'true',
                bSearchable_3: 'true',
                bSearchable_4: 'true',
                bSearchable_5: 'true',
                bSearchable_6: 'false',
                bSearchable_7: 'false',
                bSearchable_8: 'false',
                sSearch: ''
            },
            success: function(response) {
                console.log('AJAX Response:', response);
                
                var debugHtml = '<h4>✅ AJAX Call Successful</h4>';
                debugHtml += '<p><strong>Total Records:</strong> ' + response.iTotalRecords + '</p>';
                debugHtml += '<p><strong>Filtered Records:</strong> ' + response.iTotalDisplayRecords + '</p>';
                debugHtml += '<p><strong>Data Rows:</strong> ' + response.aaData.length + '</p>';
                
                if(response.debug_info) {
                    debugHtml += '<h5>Debug Info:</h5>';
                    debugHtml += '<pre>' + JSON.stringify(response.debug_info, null, 2) + '</pre>';
                }
                
                if(response.aaData.length > 0) {
                    debugHtml += '<h5>Sample Data (First Row):</h5>';
                    debugHtml += '<pre>' + JSON.stringify(response.aaData[0], null, 2) + '</pre>';
                }
                
                $('#debug-info').html(debugHtml);
                
                // Update test results
                if(response.aaData.length > 0) {
                    $('#test-results').html('<div class="alert alert-success">✅ <strong>SUCCESS:</strong> DataTable berhasil mengambil ' + response.aaData.length + ' data dari database!</div>');
                } else {
                    $('#test-results').html('<div class="alert alert-warning">⚠️ <strong>WARNING:</strong> DataTable berhasil diakses tapi tidak ada data yang dikembalikan.</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', xhr.responseText);
                
                var errorHtml = '<h4>❌ AJAX Call Failed</h4>';
                errorHtml += '<p><strong>Status:</strong> ' + status + '</p>';
                errorHtml += '<p><strong>Error:</strong> ' + error + '</p>';
                errorHtml += '<p><strong>Response:</strong></p>';
                errorHtml += '<pre>' + xhr.responseText + '</pre>';
                
                $('#debug-info').html(errorHtml);
                $('#test-results').html('<div class="alert alert-danger">❌ <strong>ERROR:</strong> Tidak dapat mengakses DataTable. Periksa debug info di atas.</div>');
            }
        });
        
        // Test 2: Initialize actual DataTable
        var table = $('#test-datatable').DataTable({
            "processing": true,
            "serverSide": true,
            "ajax": {
                "url": "sw-datatable.php",
                "type": "GET",
                "error": function(xhr, error, thrown) {
                    console.error('DataTable Error:', error);
                    console.error('Response:', xhr.responseText);
                    
                    $('#test-results').append('<div class="alert alert-danger">❌ <strong>DataTable Error:</strong> ' + error + '</div>');
                }
            },
            "columns": [
                { "data": 0, "orderable": false, "searchable": false, "width": "50px" },
                { "data": 1, "orderable": true, "searchable": true },
                { "data": 2, "orderable": true, "searchable": true },
                { "data": 3, "orderable": true, "searchable": true },
                { "data": 4, "orderable": true, "searchable": true },
                { "data": 5, "orderable": true, "searchable": true },
                { "data": 6, "orderable": true, "searchable": false },
                { "data": 7, "orderable": false, "searchable": false },
                { "data": 8, "orderable": false, "searchable": false, "width": "150px" }
            ],
            "order": [[ 1, "desc" ]],
            "pageLength": 10,
            "language": {
                "processing": "Memproses...",
                "lengthMenu": "Tampilkan _MENU_ data per halaman",
                "zeroRecords": "Data tidak ditemukan",
                "info": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                "infoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
                "infoFiltered": "(disaring dari _MAX_ total data)",
                "search": "Cari:",
                "paginate": {
                    "first": "Pertama",
                    "last": "Terakhir",
                    "next": "Selanjutnya",
                    "previous": "Sebelumnya"
                }
            },
            "drawCallback": function(settings) {
                console.log('DataTable drawn with', settings.json.iTotalRecords, 'total records');
                
                if(settings.json.iTotalRecords > 0) {
                    $('#test-results').append('<div class="alert alert-success">✅ <strong>DataTable SUCCESS:</strong> Tabel berhasil menampilkan ' + settings.json.iTotalRecords + ' data!</div>');
                } else {
                    $('#test-results').append('<div class="alert alert-info">ℹ️ <strong>DataTable INFO:</strong> Tabel berhasil dimuat tapi tidak ada data.</div>');
                }
            }
        });
        
        // Add refresh button functionality
        window.refreshTestTable = function() {
            table.ajax.reload();
            $('#test-results').html('<p>Refreshing table...</p>');
        };
    });
    </script>
    
    <div style="position: fixed; bottom: 20px; right: 20px;">
        <button onclick="refreshTestTable()" class="btn btn-success">
            <i class="fa fa-refresh"></i> Refresh Table
        </button>
    </div>
</body>
</html>
