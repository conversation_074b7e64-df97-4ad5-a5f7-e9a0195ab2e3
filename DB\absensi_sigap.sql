-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- <PERSON><PERSON><PERSON> pembuatan: 19 Jul 2025 pada 15.57
-- Versi server: 10.4.27-MariaDB
-- Versi PHP: 7.4.33

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `absensi_sigap`
--

-- --------------------------------------------------------

--
-- <PERSON><PERSON><PERSON> dari tabel `building`
--

  CREATE TABLE `building` (
    `building_id` int(8) NOT NULL,
    `code` varchar(20) NOT NULL,
    `name` varchar(50) NOT NULL,
    `address` varchar(100) NOT NULL,
    `latitude_longtitude` varchar(150) NOT NULL,
    `radius` int(5) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `building`
  --

  INSERT INTO `building` (`building_id`, `code`, `name`, `address`, `latitude_longtitude`, `radius`) VALUES
  (7, '2023/2E622/2023-09-1', 'SPM KANTOR PUSAT', 'KELAPA TUJUH', '-4.8462225,104.9076532', 50),
  (8, '2023/C9909/2023-09-1', 'SPM KANTOR CABANG', 'KEBON EMPAT', '-4.840112,104.886612', 50),
  (9, '2025/1FAAA/2025-07-1', 'KANTOR CABANG TANGERANG', 'DASANA INDAH', '-6.25583417906717,106.60433292388917', 50);

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `cuty`
  --

  CREATE TABLE `cuty` (
    `cuty_id` int(11) NOT NULL,
    `employees_id` int(11) NOT NULL,
    `cuty_start` date NOT NULL,
    `cuty_end` date NOT NULL,
    `date_work` date NOT NULL,
    `cuty_total` int(5) NOT NULL,
    `cuty_description` varchar(100) NOT NULL,
    `cuty_status` int(2) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `employees`
  --

  CREATE TABLE `employees` (
    `id` int(11) NOT NULL,
    `employees_code` varchar(30) NOT NULL,
    `employees_email` varchar(30) NOT NULL,
    `employees_password` varchar(100) NOT NULL,
    `employees_name` varchar(50) NOT NULL,
    `position_id` int(5) NOT NULL,
    `shift_id` int(11) NOT NULL,
    `building_id` int(11) NOT NULL,
    `photo` varchar(100) NOT NULL,
    `created_login` datetime NOT NULL,
    `created_cookies` varchar(70) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `employees`
  --

  INSERT INTO `employees` (`id`, `employees_code`, `employees_email`, `employees_password`, `employees_name`, `position_id`, `shift_id`, `building_id`, `photo`, `created_login`, `created_cookies`) VALUES
  (36, '12345', '<EMAIL>', 'd1321497453c5367f4f8987b3156319ace28da05e36163d91b82d200686f963d', 'Ikmal Hanaan Zikri', 8, 1, 8, '', '2025-07-07 20:13:23', '01c52a17cf38c3a4ba604838efe204a4'),
  (37, '1234567', '<EMAIL>', 'd1321497453c5367f4f8987b3156319ace28da05e36163d91b82d200686f963d', 'Saddan Husein', 8, 1, 8, '', '2025-07-07 20:13:23', '01c52a17cf38c3a4ba604838efe204a4'),
  (38, '123456789012', '<EMAIL>', 'dc14f914538c74f38dab30cd25aa669c2b0a979f7679004ced651d75ae7d931c', 'Taufik', 1, 1, 8, '', '2025-07-08 00:00:00', '475a92cd68c63003afbda8cb895e0607'),
  (39, '0700168', '<EMAIL>', 'c23104fce6c22dabe892f5ca1c74da5300eb55edcca271842f59fe83715f3acc', 'NANDA', 8, 1, 9, '2025-07-1653d747371c07bf5a9830d3ca1c2abffe.jpg', '2025-07-16 17:03:34', '961535ecf9991f3b21e2d212727960e7'),
  (40, '120315', '<EMAIL>', '8d83beea3f17295dd7f85e0935c50c18a949c7bfb8efd5544be1580eb1a657e0', 'Greata', 8, 1, 9, '', '2025-07-16 12:43:49', '4d4a8619bf8ea93104ac53684bab24bb'),
  (41, '1982989', '<EMAIL>', '67b27465b4dafca2269a182612aee716be3d0824b4db00c91dd0591d5f61761c', 'Setiawan', 1, 1, 8, '', '2025-07-16 17:07:49', '3e12cb83f899c56dfcfb9fa089e3cd7a');

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `holiday`
  --

  CREATE TABLE `holiday` (
    `holiday_id` int(11) NOT NULL,
    `holiday_date` date NOT NULL,
    `description` varchar(150) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `permission`
  --

  CREATE TABLE `permission` (
    `permission_id` int(11) NOT NULL,
    `employees_id` int(11) NOT NULL,
    `permission_name` varchar(35) NOT NULL,
    `permission_date` date NOT NULL,
    `permission_date_finish` date NOT NULL,
    `permission_description` text NOT NULL,
    `files` varchar(150) NOT NULL,
    `type` varchar(20) NOT NULL,
    `date` date NOT NULL,
    `status` varchar(2) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `permission`
  --

  INSERT INTO `permission` (`permission_id`, `employees_id`, `permission_name`, `permission_date`, `permission_date_finish`, `permission_description`, `files`, `type`, `date`, `status`) VALUES
  (24, 30, 'SITI ARIYANI', '2023-09-29', '2023-09-29', 'Izin pulang karen sodara ada yang meninggal dunia ', '2023-09-29-30-16959631208361281629047648887746jpg.jpg', 'Izin', '2023-09-29', '1'),
  (25, 18, 'CITRA ANINDA', '2023-10-10', '2023-10-10', 'Izin mamak oprasi', '2023-10-10-18-imagejpg.jpg', 'Izin', '2023-10-10', '1'),
  (26, 17, 'ESTRI YASRIATNI', '2023-10-10', '2023-10-10', 'Sakit ', '2023-10-10-17-16969236971165629210396166219880jpg.jpg', 'Sakit', '2023-10-10', '1'),
  (27, 29, 'NADINE AULIA PUTRI', '2023-10-11', '2023-10-12', 'Sakit', '2023-10-11-29-16969973231841333317607225418429jpg.jpg', 'Sakit', '2023-10-11', '1'),
  (28, 30, 'SITI ARIYANI', '2023-10-11', '2023-10-11', 'Sakit lambung ', '2023-10-11-30-16970094455307005265242504777590jpg.jpg', 'Izin', '2023-10-11', '1'),
  (29, 28, 'SARI SUBHANI', '2023-11-06', '2023-11-06', 'ada acara keluarga ', '2023-11-06-28-img20220705111714jpg.jpg', 'Izin', '2023-11-06', '1'),
  (30, 29, 'NADINE AULIA PUTRI', '2023-11-09', '2023-11-09', 'Izin pulang', '2023-11-09-29-16995156741344832991131266798815jpg.jpg', 'Izin', '2023-11-09', '1'),
  (31, 30, 'SITI ARIYANI', '2023-11-09', '2023-11-09', 'Izin pulang, karna ingin revisi laporan ', '2023-11-09-30-16995157337032599512742173132454jpg.jpg', 'Izin', '2023-11-09', '1'),
  (32, 28, 'SARI SUBHANI', '2023-11-09', '2023-11-09', 'Izin pulang karna ingin revisi laporan ', '2023-11-09-28-16995158328944680542089033851655jpg.jpg', 'Izin', '2023-11-09', '1'),
  (33, 29, 'NADINE AULIA PUTRI', '2023-11-13', '2023-11-13', 'Izin tidak bisa masuk pkl karena sakit', '2023-11-13-29-1699832691153116277273769512157jpg.jpg', 'Sakit', '2023-11-13', '1');

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `position`
  --

  CREATE TABLE `position` (
    `position_id` int(5) NOT NULL,
    `position_name` varchar(30) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `position`
  --

  INSERT INTO `position` (`position_id`, `position_name`) VALUES
  (1, 'STAFF'),
  (8, 'BOD');

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `presence`
  --

  CREATE TABLE `presence` (
    `presence_id` int(11) NOT NULL,
    `employees_id` int(11) NOT NULL,
    `presence_date` date NOT NULL,
    `time_in` time NOT NULL,
    `time_out` time NOT NULL,
    `picture_in` text NOT NULL,
    `picture_out` varchar(150) NOT NULL,
    `present_id` int(11) NOT NULL COMMENT 'Masuk,Pulang,Tidak Hadir',
    `latitude_longtitude_in` varchar(200) NOT NULL,
    `latitude_longtitude_out` varchar(200) NOT NULL,
    `information` varchar(50) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `presence`
  --

  INSERT INTO `presence` (`presence_id`, `employees_id`, `presence_date`, `time_in`, `time_out`, `picture_in`, `picture_out`, `present_id`, `latitude_longtitude_in`, `latitude_longtitude_out`, `information`) VALUES
  (11, 30, '2023-09-18', '09:28:52', '17:03:23', 'absen-in-30-1695004132.jpg', 'absen-out-30-1695031403.jpg', 1, '-4.840136,104.886572', '-4.8402176,104.8864053', ''),
  (12, 28, '2023-09-18', '09:51:02', '17:07:35', 'absen-in-28-1695005462.jpg', 'absen-out-28-1695031655.jpg', 1, '-4.8401401,104.8865721', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401185,104.8865476</font></font>', ''),
  (13, 29, '2023-09-18', '09:56:53', '17:04:24', 'absen-in-29-1695005813.jpg', 'absen-out-29-1695031464.jpg', 1, '-4.840136,104.886572', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840055,104.8864854</font></font>', ''),
  (14, 30, '2023-09-19', '07:03:54', '17:00:36', 'absen-in-30-1695081834.jpg', 'absen-out-30-1695117636.jpg', 1, '-4.8400153,104.8862423', '-4.8403224,104.88648', ''),
  (15, 29, '2023-09-19', '07:07:44', '17:01:48', 'absen-in-29-1695082064.jpg', 'absen-out-29-1695117708.jpg', 1, '-4.8400153,104.8862423', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840339,104.8864836</font></font>', ''),
  (16, 28, '2023-09-19', '07:10:38', '17:01:12', 'absen-in-28-1695082238.jpg', 'absen-out-28-1695117672.jpg', 1, '-4.8399204,104.8861528', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403334,104.8864897</font></font>', ''),
  (17, 30, '2023-09-20', '07:04:33', '17:01:20', 'absen-in-30-1695168273.jpg', 'absen-out-30-1695204080.jpg', 1, '-4.8401205,104.886547', '-4.8401386,104.886576', ''),
  (18, 28, '2023-09-20', '07:08:49', '17:02:46', 'absen-in-28-1695168529.jpg', 'absen-out-28-1695204166.jpg', 1, '-4.8401212,104.8865653', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401386,104.886576</font></font>', ''),
  (19, 29, '2023-09-20', '07:34:49', '17:01:55', 'absen-in-29-1695170089.jpg', 'absen-out-29-1695204115.jpg', 1, '-4.8401419,104.8865743', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401386,104.886576</font></font>', ''),
  (20, 30, '2023-09-21', '07:07:47', '17:00:54', 'absen-in-30-1695254867.jpg', 'absen-out-30-1695290454.jpg', 1, '-4.8400703,104.8864099', '-4.8403382,104.8864716', ''),
  (21, 29, '2023-09-21', '07:08:20', '17:01:18', 'absen-in-29-1695254900.jpg', 'absen-out-29-1695290478.jpg', 1, '-4.8400703,104.8864099', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840326,104.8864585</font></font>', ''),
  (22, 28, '2023-09-21', '07:12:59', '17:01:42', 'absen-in-28-1695255179.jpg', 'absen-out-28-1695290502.jpg', 1, '-4.8400703,104.8864099', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403405,104.8864603</font></font>', ''),
  (23, 18, '2023-09-21', '10:07:40', '17:04:59', 'absen-in-18-1695265660.jpg', 'absen-out-18-1695290699.jpg', 1, '-4.846187579062676,104.90768221341025', '-4.846182608551277,104.90772242431748', 'Tes absen pertama\r\n'),
  (24, 19, '2023-09-21', '10:08:39', '17:04:58', 'absen-in-19-1695265719.jpg', 'absen-out-19-1695290698.jpg', 1, '-4.846282803249193,104.90761251159776', '-4.8462918012343374,104.90763530173639', 'Tes absen pertama'),
  (25, 24, '2023-09-21', '10:08:51', '00:00:00', 'absen-in-24-1695265731.jpg', '', 1, '-4.84622,104.9076646', '', 'Tes absen pertama'),
  (26, 22, '2023-09-21', '10:16:02', '17:06:55', 'absen-in-22-1695266162.jpg', 'absen-out-22-1695290815.jpg', 1, '-4.8462174,104.9076655', '-4.8462172,104.9076677', 'Tes absen pertama'),
  (27, 21, '2023-09-21', '10:16:04', '17:04:23', 'absen-in-21-1695266164.jpg', 'absen-out-21-1695290663.jpg', 1, '-4.8462151,104.907648', '-4.8462201,104.9076435', 'Tes hadir'),
  (28, 30, '2023-09-22', '07:08:20', '00:00:00', 'absen-in-30-1695341300.jpg', '', 1, '-4.8400744,104.8863658', '', ''),
  (29, 29, '2023-09-22', '07:08:44', '00:00:00', 'absen-in-29-1695341324.jpg', '', 1, '-4.8401023,104.8864003', '', ''),
  (30, 28, '2023-09-22', '07:15:27', '00:00:00', 'absen-in-28-1695341727.jpg', '', 1, '-4.8403372,104.8864827', '', ''),
  (31, 18, '2023-09-22', '07:27:11', '17:03:59', 'absen-in-18-1695342431.jpg', 'absen-out-18-1695377039.jpg', 1, '-4.84614932697346,104.90762904544363', '-4.846187109869548,104.90768083644055', 'Pecah ban'),
  (32, 19, '2023-09-22', '07:28:45', '17:05:56', 'absen-in-19-1695342525.jpg', 'absen-out-19-1695377156.jpg', 1, '-4.846294565788219,104.90764223714221', '-4.846291182457404,104.90755519973804', ''),
  (33, 22, '2023-09-22', '07:29:32', '17:05:16', 'absen-in-22-1695342572.jpg', 'absen-out-22-1695377116.jpg', 1, '-4.8462154,104.9076624', '-4.8462166,104.9076658', 'Gak bisa absen (keterangan jauh dari radius)'),
  (34, 22, '2023-09-23', '07:11:19', '17:05:56', 'absen-in-22-1695427879.jpg', 'absen-out-22-1695463556.jpg', 1, '-4.8461187,104.9075539', '-4.8462181,104.9076619', ''),
  (35, 19, '2023-09-23', '07:16:09', '17:04:40', 'absen-in-19-1695428169.jpg', 'absen-out-19-1695463480.jpg', 1, '-4.8463543777596465,104.90756788855911', '-4.846282797338197,104.90761243423641', ''),
  (36, 18, '2023-09-23', '07:25:56', '17:04:48', 'absen-in-18-1695428756.jpg', 'absen-out-18-1695463488.jpg', 1, '-4.846213798795661,104.9076447331341', '-4.846187109869548,104.90768083644055', 'Balik lagi ngambil hp toko admin 2'),
  (37, 21, '2023-09-23', '07:28:46', '17:04:06', 'absen-in-21-1695428926.jpg', 'absen-out-21-1695463446.jpg', 1, '-4.8462189,104.9076479', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8460779,104.9077675</font></font>', 'Tidak bisa absen'),
  (38, 30, '2023-09-23', '07:38:22', '17:08:04', 'absen-in-30-1695429502.jpg', 'absen-out-30-1695463684.jpg', 1, '-4.8401375,104.8865746', '-4.840151,104.886615', ''),
  (39, 28, '2023-09-23', '07:40:14', '17:07:21', 'absen-in-28-1695429614.jpg', 'absen-out-28-1695463641.jpg', 1, '-4.8401375,104.8865746', '-4.840151,104.886615', ''),
  (40, 29, '2023-09-23', '07:41:48', '17:08:53', 'absen-in-29-1695429708.jpg', 'absen-out-29-1695463733.jpg', 1, '-4.8401375,104.8865746', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840151,104.886615</font></font>', ''),
  (41, 17, '2023-09-23', '10:19:12', '17:08:33', 'absen-in-17-1695439152.jpg', 'absen-out-17-1695463713.jpg', 1, '-4.8401436,104.8865714', '-4.8401599,104.8863266', ''),
  (42, 20, '2023-09-23', '10:36:26', '00:00:00', 'absen-in-20-1695440186.jpg', '', 1, '-4.8402776,104.8864652', '', ''),
  (43, 29, '2023-09-25', '07:00:35', '17:03:50', 'absen-in-29-1695600035.jpg', 'absen-out-29-1695636230.jpg', 1, '-4.840118,104.8865507', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403368,104.8864863</font></font>', ''),
  (44, 30, '2023-09-25', '07:00:46', '17:03:05', 'absen-in-30-1695600046.jpg', 'absen-out-30-1695636185.jpg', 1, '-4.8401147,104.8864279', '-4.840329,104.8864829', ''),
  (45, 22, '2023-09-25', '07:07:31', '17:03:09', 'absen-in-22-1695600451.jpg', 'absen-out-22-1695636189.jpg', 1, '-4.8461921,104.9076194', '-4.8461104,104.9075395', ''),
  (46, 21, '2023-09-25', '07:10:30', '17:04:40', 'absen-in-21-1695600630.jpg', 'absen-out-21-1695636280.jpg', 1, '-4.8461806,104.9076087', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8461982,104.9076354</font></font>', ''),
  (47, 19, '2023-09-25', '07:14:03', '17:04:21', 'absen-in-19-1695600843.jpg', 'absen-out-19-1695636261.jpg', 1, '-4.846278395638211,104.90753001792497', '-4.846305579370336,104.90766058917897', ''),
  (48, 28, '2023-09-25', '07:15:46', '17:03:57', 'absen-in-28-1695600946.jpg', 'absen-out-28-1695636237.jpg', 1, '-4.839975,104.8862631', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840329,104.8864829</font></font>', ''),
  (49, 18, '2023-09-25', '07:20:12', '17:02:35', 'absen-in-18-1695601212.jpg', 'absen-out-18-1695636155.jpg', 1, '-4.846414523152555,104.90768653423964', '-4.846187109869548,104.90768083644055', ''),
  (50, 20, '2023-09-25', '07:20:21', '17:05:35', 'absen-in-20-1695601221.jpg', 'absen-out-20-1695636335.jpg', 1, '-4.8403384,104.8864807', '-4.8400934,104.8864023', ''),
  (52, 22, '2023-09-26', '07:06:14', '17:04:34', 'absen-in-22-1695686774.jpg', 'absen-out-22-1695722674.jpg', 1, '-4.8462159,104.9076655', '-4.8462138,104.9076643', ''),
  (53, 30, '2023-09-26', '07:09:52', '17:03:38', 'absen-in-30-1695686992.jpg', 'absen-out-30-1695722618.jpg', 1, '-4.8400528,104.8863233', '-4.8400831,104.8865286', ''),
  (54, 20, '2023-09-26', '07:10:11', '17:00:35', 'absen-in-20-1695687011.jpg', 'absen-out-20-1695722435.jpg', 1, '-4.840142,104.8865538', '-4.8401522,104.8865637', ''),
  (55, 21, '2023-09-26', '07:10:27', '17:03:53', 'absen-in-21-1695687027.jpg', 'absen-out-21-1695722633.jpg', 1, '-4.846196,104.9075948', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8461679,104.9075654</font></font>', ''),
  (56, 17, '2023-09-26', '07:10:59', '17:05:37', 'absen-in-17-1695687059.jpg', 'absen-out-17-1695722737.jpg', 1, '-4.840185,104.8866233', '-4.840095,104.8865767', ''),
  (57, 19, '2023-09-26', '07:14:55', '17:05:06', 'absen-in-19-1695687295.jpg', 'absen-out-19-1695722706.jpg', 1, '-4.846319138477306,104.90761050944747', '-4.846282997299872,104.90762546311655', ''),
  (58, 29, '2023-09-26', '07:19:25', '17:04:30', 'absen-in-29-1695687565.jpg', 'absen-out-29-1695722670.jpg', 1, '-4.8401329,104.8865607', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8400831,104.8865286</font></font>', ''),
  (59, 18, '2023-09-26', '07:19:46', '17:05:35', 'absen-in-18-1695687586.jpg', 'absen-out-18-1695722735.jpg', 1, '-4.8462026162270595,104.90752543691833', '-4.846187109869548,104.90768083644055', ''),
  (60, 28, '2023-09-26', '07:34:15', '17:03:03', 'absen-in-28-1695688455.jpg', 'absen-out-28-1695722583.jpg', 1, '-4.8403524,104.8864551', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8400831,104.8865286</font></font>', ''),
  (61, 30, '2023-09-27', '07:07:19', '16:59:54', 'absen-in-30-1695773239.jpg', 'absen-out-30-1695808794.jpg', 1, '-4.8401029,104.8865361', '-4.8401363,104.886573', ''),
  (62, 29, '2023-09-27', '07:08:24', '17:00:21', 'absen-in-29-1695773304.jpg', 'absen-out-29-1695808821.jpg', 1, '-4.8401316,104.8865674', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8402829,104.8865108</font></font>', ''),
  (63, 21, '2023-09-27', '07:09:26', '17:03:28', 'absen-in-21-1695773366.jpg', 'absen-out-21-1695809008.jpg', 1, '-4.8461787,104.9076057', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8461962,104.9077499</font></font>', ''),
  (64, 20, '2023-09-27', '07:10:27', '17:01:15', 'absen-in-20-1695773427.jpg', 'absen-out-20-1695808875.jpg', 1, '-4.8401327,104.8865612', '-4.8403544,104.8864706', ''),
  (65, 22, '2023-09-27', '07:13:37', '17:03:03', 'absen-in-22-1695773617.jpg', 'absen-out-22-1695808983.jpg', 1, '-4.8462186,104.9076593', '-4.8462191,104.9076574', ''),
  (66, 28, '2023-09-27', '07:14:08', '17:00:35', 'absen-in-28-1695773648.jpg', 'absen-out-28-1695808835.jpg', 1, '-4.8401177,104.8865389', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401363,104.886573</font></font>', ''),
  (67, 19, '2023-09-27', '07:15:09', '17:02:10', 'absen-in-19-1695773709.jpg', 'absen-out-19-1695808930.jpg', 1, '-4.846305857640137,104.9074280578259', '-4.846296332738687,104.90765289211384', ''),
  (68, 17, '2023-09-27', '07:17:43', '17:02:09', 'absen-in-17-1695773863.jpg', 'absen-out-17-1695808929.jpg', 1, '-4.8401456,104.8865716', '-4.8401318,104.8865652', ''),
  (69, 18, '2023-09-27', '07:19:23', '17:00:59', 'absen-in-18-1695773963.jpg', 'absen-out-18-1695808859.jpg', 1, '-4.8463505581217134,104.90763250262796', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.846299077276719,104.90760329430508</font></font>', ''),
  (70, 30, '2023-09-28', '06:55:36', '17:00:19', 'absen-in-30-1695858936.jpg', 'absen-out-30-1695895219.jpg', 1, '-4.8401117,104.8865444', '-4.8401381,104.8865728', ''),
  (71, 28, '2023-09-28', '07:01:07', '17:01:53', 'absen-in-28-1695859267.jpg', 'absen-out-28-1695895313.jpg', 1, '-4.8401129,104.8865628', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403464,104.8864775</font></font>', ''),
  (72, 21, '2023-09-28', '07:09:37', '00:00:00', 'absen-in-21-1695859777.jpg', '', 1, '-4.846217,104.9076496', '', ''),
  (73, 17, '2023-09-28', '07:11:55', '17:02:55', 'absen-in-17-1695859915.jpg', 'absen-out-17-1695895375.jpg', 1, '-4.8401211,104.8865652', '-4.8401211,104.8865652', ''),
  (74, 20, '2023-09-28', '07:14:51', '17:01:51', 'absen-in-20-1695860091.jpg', 'absen-out-20-1695895311.jpg', 1, '-4.8402892,104.8865858', '-4.8401309,104.8865693', ''),
  (75, 19, '2023-09-28', '07:15:26', '17:03:02', 'absen-in-19-1695860126.jpg', 'absen-out-19-1695895382.jpg', 1, '-4.846102138819243,104.90732661568146', '-4.846315633339695,104.90768550465971', ''),
  (76, 29, '2023-09-28', '07:20:15', '16:53:35', 'absen-in-29-1695860415.jpg', 'absen-out-29-1695894815.jpg', 1, '-4.8401327,104.8865656', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8404118,104.8868752</font></font>', ''),
  (77, 18, '2023-09-28', '07:42:21', '17:06:35', 'absen-in-18-1695861741.jpg', 'absen-out-18-1695895595.jpg', 1, '-4.84622881285184,104.90748522967225', '-4.846305007825596,104.90762820077724', 'Lupa absen bang'),
  (78, 22, '2023-09-28', '07:43:00', '17:06:56', 'absen-in-22-1695861780.jpg', 'absen-out-22-1695895616.jpg', 1, '-4.8461721,104.9075587', '-4.8462185,104.9076595', 'Gak bisa absen ( Keterangan jauh dari radius )'),
  (79, 30, '2023-09-29', '07:02:44', '00:00:00', 'absen-in-30-1695945764.jpg', '', 1, '-4.840097,104.886519', '', ''),
  (80, 22, '2023-09-29', '07:05:02', '17:03:44', 'absen-in-22-1695945902.jpg', 'absen-out-22-1695981824.jpg', 1, '-4.8462014,104.9076401', '-4.8462186,104.9076598', ''),
  (81, 20, '2023-09-29', '07:10:29', '17:00:13', 'absen-in-20-1695946229.jpg', 'absen-out-20-1695981613.jpg', 1, '-4.8401141,104.8865521', '-4.8401256,104.8865758', ''),
  (82, 29, '2023-09-29', '07:10:38', '17:00:38', 'absen-in-29-1695946238.jpg', 'absen-out-29-1695981638.jpg', 1, '-4.8402706,104.8865851', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401391,104.8865692</font></font>', ''),
  (83, 28, '2023-09-29', '07:12:02', '17:01:56', 'absen-in-28-1695946322.jpg', 'absen-out-28-1695981716.jpg', 1, '-4.8402706,104.8865851', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401554,104.8865615</font></font>', ''),
  (84, 19, '2023-09-29', '07:15:23', '17:03:52', 'absen-in-19-1695946523.jpg', 'absen-out-19-1695981832.jpg', 1, '-4.846309971188586,104.90763997644068', '-4.846291647634887,104.9076315125218', ''),
  (85, 21, '2023-09-29', '07:16:33', '17:02:11', 'absen-in-21-1695946593.jpg', 'absen-out-21-1695981731.jpg', 1, '-4.8465455,104.9076772', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8461674,104.9076033</font></font>', 'Lupa kata sandiðŸ˜Š'),
  (86, 18, '2023-09-29', '07:17:44', '17:03:59', 'absen-in-18-1695946664.jpg', 'absen-out-18-1695981839.jpg', 1, '-4.8464062890138875,104.90754379857103', '-4.846310267790975,104.90767821647485', ''),
  (87, 17, '2023-09-29', '07:18:17', '17:03:15', 'absen-in-17-1695946697.jpg', 'absen-out-17-1695981795.jpg', 1, '-4.8400867,104.8866251', '-4.8401285,104.8866387', ''),
  (88, 30, '2023-09-29', '00:00:00', '00:00:00', '', '', 3, '', '', ''),
  (89, 20, '2023-09-30', '07:02:32', '00:00:00', 'absen-in-20-1696032152.jpg', '', 1, '-4.8401221,104.8865281', '', ''),
  (90, 22, '2023-09-30', '07:06:18', '17:06:20', 'absen-in-22-1696032378.jpg', 'absen-out-22-1696068380.jpg', 1, '-4.8462031,104.9076174', '-4.8462185,104.9076595', ''),
  (91, 30, '2023-09-30', '07:11:25', '17:00:32', 'absen-in-30-1696032685.jpg', 'absen-out-30-1696068032.jpg', 1, '-4.8400969,104.8865019', '-4.8403189,104.886487', ''),
  (92, 21, '2023-09-30', '07:13:56', '17:07:19', 'absen-in-21-1696032836.jpg', 'absen-out-21-1696068439.jpg', 1, '-4.8461728,104.9075931', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462171,104.9076519</font></font>', ''),
  (93, 18, '2023-09-30', '07:15:37', '00:00:00', 'absen-in-18-1696032937.jpg', '', 1, '-4.846227308402688,104.90748697496234', '', ''),
  (94, 28, '2023-09-30', '07:16:04', '17:00:50', 'absen-in-28-1696032964.jpg', 'absen-out-28-1696068050.jpg', 1, '-4.8399443,104.8863184', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401381,104.8863597</font></font>', ''),
  (95, 19, '2023-09-30', '07:16:19', '17:06:45', 'absen-in-19-1696032979.jpg', 'absen-out-19-1696068405.jpg', 1, '-4.846229659787154,104.90736878819702', '-4.846111160032071,104.90770810078155', ''),
  (96, 17, '2023-09-30', '07:19:09', '17:03:58', 'absen-in-17-1696033149.jpg', 'absen-out-17-1696068238.jpg', 1, '-4.8401319,104.8865701', '-4.840325,104.8864629', ''),
  (97, 29, '2023-09-30', '07:21:21', '17:00:39', 'absen-in-29-1696033281.jpg', 'absen-out-29-1696068039.jpg', 1, '-4.8401247,104.8865701', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8402656,104.8865163</font></font>', ''),
  (98, 17, '2023-10-01', '07:26:54', '17:01:07', 'absen-in-17-1696120014.jpg', 'absen-out-17-1696154467.jpg', 1, '-4.8403166,104.8864251', '-4.8403043,104.8864674', ''),
  (99, 29, '2023-10-02', '07:06:10', '17:01:41', 'absen-in-29-1696205170.jpg', 'absen-out-29-1696240901.jpg', 1, '-4.8401019,104.8865099', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401541,104.8865617</font></font>', ''),
  (100, 30, '2023-10-02', '07:07:14', '17:00:43', 'absen-in-30-1696205234.jpg', 'absen-out-30-1696240843.jpg', 1, '-4.8400227,104.8863551', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403442,104.8864802</font></font>', ''),
  (101, 28, '2023-10-02', '07:12:05', '17:01:16', 'absen-in-28-1696205525.jpg', 'absen-out-28-1696240876.jpg', 1, '-4.8400897,104.8864987', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403332,104.886482</font></font>', ''),
  (102, 20, '2023-10-02', '07:12:31', '17:00:29', 'absen-in-20-1696205551.jpg', 'absen-out-20-1696240829.jpg', 1, '-4.839988,104.8863318', '-4.8401717,104.8865861', ''),
  (103, 19, '2023-10-02', '07:14:41', '17:06:36', 'absen-in-19-1696205681.jpg', 'absen-out-19-1696241196.jpg', 1, '-4.846133401033106,104.90735916232748', '-4.846282847409157,104.90762480470916', ''),
  (104, 21, '2023-10-02', '07:15:39', '17:06:27', 'absen-in-21-1696205739.jpg', 'absen-out-21-1696241187.jpg', 1, '-4.8462135,104.9076432', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462163,104.9076467</font></font>', ''),
  (105, 22, '2023-10-02', '07:16:35', '17:08:02', 'absen-in-22-1696205795.jpg', 'absen-out-22-1696241282.jpg', 1, '-4.8462212,104.9077604', '-4.8462199,104.9076572', ''),
  (106, 17, '2023-10-02', '07:18:03', '17:02:43', 'absen-in-17-1696205883.jpg', 'absen-out-17-1696240963.jpg', 1, '-4.8401392,104.8865769', '-4.840122,104.886569', ''),
  (107, 18, '2023-10-02', '16:32:37', '17:06:44', 'absen-in-18-1696239157.jpg', 'absen-out-18-1696241204.jpg', 1, '-4.846397619438264,104.90753659696054', '-4.846309896011546,104.90766937140772', 'Lupa absen di karenakan ada tragedi kipas jatuh di'),
  (108, 30, '2023-10-03', '07:02:17', '00:00:00', 'absen-in-30-1696291337.jpg', '', 1, '-4.8400659,104.8863764', '', ''),
  (109, 22, '2023-10-03', '07:03:08', '17:08:29', 'absen-in-22-1696291388.jpg', 'absen-out-22-1696327709.jpg', 1, '-4.8461027,104.9075401', '-4.8462209,104.9076577', ''),
  (110, 21, '2023-10-03', '07:07:03', '17:08:13', 'absen-in-21-1696291623.jpg', 'absen-out-21-1696327693.jpg', 1, '-4.8461974,104.9076125', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462165,104.9076463</font></font>', ''),
  (111, 28, '2023-10-03', '07:10:43', '00:00:00', 'absen-in-28-1696291843.jpg', '', 1, '-4.8400796,104.8865674', '', ''),
  (112, 29, '2023-10-03', '07:14:10', '00:00:00', 'absen-in-29-1696292050.jpg', '', 1, '-4.8400281,104.8862525', '', ''),
  (113, 17, '2023-10-03', '07:14:47', '00:00:00', 'absen-in-17-1696292087.jpg', '', 1, '-4.8401235,104.8865654', '', ''),
  (114, 20, '2023-10-03', '07:17:42', '00:00:00', 'absen-in-20-1696292262.jpg', '', 1, '-4.8401353,104.8865824', '', ''),
  (115, 19, '2023-10-03', '07:20:59', '17:09:47', 'absen-in-19-1696292459.jpg', 'absen-out-19-1696327787.jpg', 1, '-4.846221076230838,104.90746566498603', '-4.846188620504262,104.90771724514474', ''),
  (116, 18, '2023-10-03', '07:32:54', '17:06:45', 'absen-in-18-1696293174.jpg', 'absen-out-18-1696327605.jpg', 1, '-4.846192788495988,104.90742762505188', '-4.846093335779261,104.90777183942876', ''),
  (117, 29, '2023-10-04', '07:06:18', '16:59:52', 'absen-in-29-1696377978.jpg', 'absen-out-29-1696413592.jpg', 1, '-4.8401141,104.8865668', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401329,104.8865734</font></font>', ''),
  (118, 30, '2023-10-04', '07:08:51', '17:00:26', 'absen-in-30-1696378131.jpg', 'absen-out-30-1696413626.jpg', 1, '-4.8401061,104.8865683', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401607,104.8865654</font></font>', ''),
  (119, 22, '2023-10-04', '07:10:24', '17:04:07', 'absen-in-22-1696378224.jpg', 'absen-out-22-1696413847.jpg', 1, '-4.8461355,104.9075617', '-4.8462198,104.9076612', ''),
  (120, 28, '2023-10-04', '07:12:48', '17:01:13', 'absen-in-28-1696378368.jpg', 'absen-out-28-1696413673.jpg', 1, '-4.8400257,104.8863183', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840328,104.8865327</font></font>', ''),
  (121, 21, '2023-10-04', '07:13:00', '17:02:44', 'absen-in-21-1696378380.jpg', 'absen-out-21-1696413764.jpg', 1, '-4.8462128,104.9076498', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462065,104.9076621</font></font>', ''),
  (122, 19, '2023-10-04', '07:15:11', '17:03:12', 'absen-in-19-1696378511.jpg', 'absen-out-19-1696413792.jpg', 1, '-4.8462417964638655,104.90751060780094', '-4.846278458855039,104.90762803885751', ''),
  (123, 17, '2023-10-04', '07:15:33', '17:03:06', 'absen-in-17-1696378533.jpg', 'absen-out-17-1696413786.jpg', 1, '-4.840124,104.8865468', '-4.8401291,104.886573', ''),
  (124, 20, '2023-10-04', '07:16:56', '17:01:13', 'absen-in-20-1696378616.jpg', 'absen-out-20-1696413673.jpg', 1, '-4.8403374,104.8865405', '-4.840101,104.8863925', ''),
  (125, 18, '2023-10-04', '07:18:30', '00:00:00', 'absen-in-18-1696378710.jpg', '', 1, '-4.846182561707608,104.90735651788424', '', ''),
  (126, 22, '2023-10-05', '07:07:13', '17:04:04', 'absen-in-22-1696464433.jpg', 'absen-out-22-1696500244.jpg', 1, '-4.8461555,104.9076344', '-4.8462109,104.9076714', ''),
  (127, 30, '2023-10-05', '07:08:21', '17:00:26', 'absen-in-30-1696464501.jpg', 'absen-out-30-1696500026.jpg', 1, '-4.8401119,104.8865495', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401375,104.8865694</font></font>', ''),
  (128, 21, '2023-10-05', '07:10:00', '17:05:08', 'absen-in-21-1696464600.jpg', 'absen-out-21-1696500308.jpg', 1, '-4.8462921,104.9077398', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462151,104.9076568</font></font>', ''),
  (129, 20, '2023-10-05', '07:13:07', '17:01:51', 'absen-in-20-1696464787.jpg', 'absen-out-20-1696500111.jpg', 1, '-4.8399986,104.8863583', '-4.8403386,104.8865353', ''),
  (130, 28, '2023-10-05', '07:16:08', '17:01:23', 'absen-in-28-1696464968.jpg', 'absen-out-28-1696500083.jpg', 1, '-4.8401119,104.8865495', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840336,104.886533</font></font>', ''),
  (131, 17, '2023-10-05', '07:19:39', '17:02:53', 'absen-in-17-1696465179.jpg', 'absen-out-17-1696500173.jpg', 1, '-4.8401245,104.8865703', '-4.8401348,104.8865721', ''),
  (132, 29, '2023-10-05', '07:19:51', '16:59:14', 'absen-in-29-1696465191.jpg', 'absen-out-29-1696499954.jpg', 1, '-4.840341,104.8865374', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401461,104.88657</font></font>', ''),
  (133, 19, '2023-10-05', '07:19:52', '17:03:37', 'absen-in-19-1696465192.jpg', 'absen-out-19-1696500217.jpg', 1, '-4.84629267318672,104.90760729180126', '-4.846329293944242,104.9075837979476', ''),
  (134, 18, '2023-10-05', '07:24:31', '17:03:33', 'absen-in-18-1696465471.jpg', 'absen-out-18-1696500213.jpg', 1, '-4.8462214530249765,104.90746875394255', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.846293020713336,104.9076046760404</font></font>', ''),
  (135, 22, '2023-10-06', '07:10:09', '17:02:25', 'absen-in-22-1696551009.jpg', 'absen-out-22-1696586545.jpg', 1, '-4.8463289,104.9076584', '-4.8462085,104.9076714', ''),
  (136, 17, '2023-10-06', '07:16:04', '17:01:41', 'absen-in-17-1696551364.jpg', 'absen-out-17-1696586501.jpg', 1, '-4.8403007,104.8865431', '-4.8403085,104.886538', ''),
  (137, 19, '2023-10-06', '08:58:09', '17:03:15', 'absen-in-19-1696557489.jpg', 'absen-out-19-1696586595.jpg', 1, '-4.846318001240331,104.90765971468649', '-4.846284171518491,104.90764574870396', 'Link tidak bisa di buka'),
  (138, 18, '2023-10-06', '08:58:44', '17:02:57', 'absen-in-18-1696557524.jpg', 'absen-out-18-1696586577.jpg', 1, '-4.846332019855897,104.90766844124856', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.846116606361637,104.90767060774675</font></font>', 'link tidak bisa di buka'),
  (139, 29, '2023-10-06', '10:13:50', '17:00:19', 'absen-in-29-1696562030.jpg', 'absen-out-29-1696586419.jpg', 1, '-4.8402745,104.8865071', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403315,104.886532</font></font>', ''),
  (140, 28, '2023-10-06', '10:15:26', '17:01:27', 'absen-in-28-1696562126.jpg', 'absen-out-28-1696586487.jpg', 1, '-4.8402745,104.8865071', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401489,104.8865495</font></font>', ''),
  (141, 30, '2023-10-06', '10:15:53', '17:00:43', 'absen-in-30-1696562153.jpg', 'absen-out-30-1696586443.jpg', 1, '-4.8402745,104.8865071', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8402862,104.8865453</font></font>', ''),
  (142, 20, '2023-10-06', '10:17:02', '17:00:53', 'absen-in-20-1696562222.jpg', 'absen-out-20-1696586453.jpg', 1, '-4.8401305,104.8865695', '-4.8403377,104.8865392', ''),
  (143, 30, '2023-10-07', '07:04:01', '17:01:50', 'absen-in-30-1696637041.jpg', 'absen-out-30-1696672910.jpg', 1, '-4.8400894,104.8865034', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840138,104.8865698</font></font>', ''),
  (144, 21, '2023-10-07', '07:06:59', '17:07:46', 'absen-in-21-1696637219.jpg', 'absen-out-21-1696673266.jpg', 1, '-4.8462003,104.9076475', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462141,104.9076664</font></font>', ''),
  (145, 29, '2023-10-07', '07:07:53', '17:02:13', 'absen-in-29-1696637273.jpg', 'absen-out-29-1696672933.jpg', 1, '-4.8400955,104.8865299', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840138,104.8865698</font></font>', ''),
  (146, 20, '2023-10-07', '07:11:55', '17:03:47', 'absen-in-20-1696637515.jpg', 'absen-out-20-1696673027.jpg', 1, '-4.8399193,104.8862801', '-4.8403392,104.8865347', ''),
  (147, 17, '2023-10-07', '07:14:45', '17:06:38', 'absen-in-17-1696637685.jpg', 'absen-out-17-1696673198.jpg', 1, '-4.8401667,104.8865689', '-4.8403382,104.8865376', ''),
  (148, 22, '2023-10-07', '07:17:31', '17:10:34', 'absen-in-22-1696637851.jpg', 'absen-out-22-1696673434.jpg', 1, '-4.8461613,104.9076844', '-4.8461689,104.9076101', ''),
  (149, 19, '2023-10-07', '07:17:31', '17:06:16', 'absen-in-19-1696637851.jpg', 'absen-out-19-1696673176.jpg', 1, '-4.846226611862137,104.90748889487067', '-4.846270229713683,104.90759163500617', ''),
  (150, 28, '2023-10-07', '07:18:22', '17:03:16', 'absen-in-28-1696637902.jpg', 'absen-out-28-1696672996.jpg', 1, '-4.8401322,104.8865749', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840138,104.8865698</font></font>', ''),
  (151, 18, '2023-10-07', '07:23:09', '17:07:50', 'absen-in-18-1696638189.jpg', 'absen-out-18-1696673270.jpg', 1, '-4.8462533523328535,104.90754117905968', '-4.846310573238575,104.90763437405786', ''),
  (152, 18, '2023-10-08', '07:32:29', '16:53:13', 'absen-in-18-1696725149.jpg', 'absen-out-18-1696758793.jpg', 1, '-4.846310124825202,104.90764700989241', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.846093424803578,104.9077304227626</font></font>', 'Kebelet bab pulang dluan'),
  (153, 21, '2023-10-09', '06:53:36', '17:02:36', 'absen-in-21-1696809216.jpg', 'absen-out-21-1696845756.jpg', 1, '-4.8461878,104.9076164', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462154,104.9076561</font></font>', ''),
  (154, 30, '2023-10-09', '07:03:53', '17:00:06', 'absen-in-30-1696809833.jpg', 'absen-out-30-1696845606.jpg', 1, '-4.839926,104.8862843', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403375,104.8865394</font></font>', ''),
  (155, 22, '2023-10-09', '07:04:21', '17:03:32', 'absen-in-22-1696809861.jpg', 'absen-out-22-1696845812.jpg', 1, '-4.846265,104.9076448', '-4.846212,104.9076739', ''),
  (156, 29, '2023-10-09', '07:10:27', '17:00:23', 'absen-in-29-1696810227.jpg', 'absen-out-29-1696845623.jpg', 1, '-4.8400154,104.8866507', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403219,104.8865361</font></font>', ''),
  (157, 28, '2023-10-09', '07:11:34', '17:00:45', 'absen-in-28-1696810294.jpg', 'absen-out-28-1696845645.jpg', 1, '-4.8400154,104.8866507', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403377,104.8865415</font></font>', ''),
  (158, 20, '2023-10-09', '07:12:25', '17:00:53', 'absen-in-20-1696810345.jpg', 'absen-out-20-1696845653.jpg', 1, '-4.8403206,104.8865402', '-4.8402473,104.8864655', ''),
  (159, 17, '2023-10-09', '07:12:39', '17:01:51', 'absen-in-17-1696810359.jpg', 'absen-out-17-1696845711.jpg', 1, '-4.8403245,104.8865401', '-4.8401306,104.8865699', ''),
  (160, 19, '2023-10-09', '07:18:14', '17:02:17', 'absen-in-19-1696810694.jpg', 'absen-out-19-1696845737.jpg', 1, '-4.846217425527154,104.90752517315597', '-4.846287490828246,104.90765167468442', ''),
  (161, 18, '2023-10-09', '07:29:52', '00:00:00', 'absen-in-18-1696811392.jpg', '', 1, '-4.8461265261681294,104.90769055706578', '', ''),
  (162, 17, '2023-10-10', '07:02:03', '00:00:00', 'absen-in-17-1696896123.jpg', '', 1, '-4.8401266,104.886555', '', ''),
  (163, 30, '2023-10-10', '07:04:32', '17:03:30', 'absen-in-30-1696896272.jpg', 'absen-out-30-1696932210.jpg', 1, '-4.8400921,104.886518', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840133,104.8865739</font></font>', ''),
  (164, 28, '2023-10-10', '07:05:19', '17:05:02', 'absen-in-28-1696896319.jpg', 'absen-out-28-1696932302.jpg', 1, '-4.8400653,104.8864185', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401348,104.8865769</font></font>', ''),
  (165, 21, '2023-10-10', '07:08:26', '17:09:46', 'absen-in-21-1696896506.jpg', 'absen-out-21-1696932586.jpg', 1, '-4.8461788,104.9076126', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462,104.9076658</font></font>', ''),
  (166, 20, '2023-10-10', '07:09:14', '17:09:02', 'absen-in-20-1696896554.jpg', 'absen-out-20-1696932542.jpg', 1, '-4.8400217,104.8863146', '-4.840337,104.88654', ''),
  (167, 29, '2023-10-10', '07:12:06', '17:03:50', 'absen-in-29-1696896726.jpg', 'absen-out-29-1696932230.jpg', 1, '-4.8401193,104.8865676', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401329,104.886577</font></font>', ''),
  (168, 22, '2023-10-10', '07:13:42', '17:07:55', 'absen-in-22-1696896822.jpg', 'absen-out-22-1696932475.jpg', 1, '-4.8462187,104.9076193', '-4.8462132,104.9076752', ''),
  (169, 19, '2023-10-10', '07:17:09', '17:09:57', 'absen-in-19-1696897029.jpg', 'absen-out-19-1696932597.jpg', 1, '-4.8462132103125795,104.90747251824499', '-4.846215411372862,104.90749173974613', ''),
  (170, 18, '2023-10-10', '00:00:00', '00:00:00', '', '', 3, '', '', ''),
  (171, 17, '2023-10-10', '00:00:00', '00:00:00', '', '', 2, '', '', ''),
  (172, 30, '2023-10-11', '07:02:15', '00:00:00', 'absen-in-30-1696982535.jpg', '', 1, '-4.8400819,104.8864823', '', ''),
  (173, 17, '2023-10-11', '07:03:01', '17:04:50', 'absen-in-17-1696982581.jpg', 'absen-out-17-1697018690.jpg', 1, '-4.840356,104.8865335', '-4.8401057,104.8865103', ''),
  (174, 28, '2023-10-11', '07:11:48', '17:09:27', 'absen-in-28-1696983108.jpg', 'absen-out-28-1697018967.jpg', 1, '-4.8401134,104.8865651', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840076,104.8865806</font></font>', ''),
  (175, 20, '2023-10-11', '07:12:04', '17:02:27', 'absen-in-20-1696983124.jpg', 'absen-out-20-1697018547.jpg', 1, '-4.8403376,104.8865406', '-4.8401338,104.8865291', ''),
  (176, 29, '2023-10-11', '07:13:58', '00:00:00', 'absen-in-29-1696983238.jpg', '', 1, '-4.8401075,104.8865241', '', ''),
  (177, 21, '2023-10-11', '07:18:33', '17:07:47', 'absen-in-21-1696983513.jpg', 'absen-out-21-1697018867.jpg', 1, '-4.84617,104.9076217', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462059,104.9076622</font></font>', ''),
  (178, 19, '2023-10-11', '07:19:50', '17:07:16', 'absen-in-19-1696983590.jpg', 'absen-out-19-1697018836.jpg', 1, '-4.846303222253677,104.90766602215325', '-4.846294430415584,104.90766812908524', ''),
  (179, 22, '2023-10-11', '07:31:30', '17:06:50', 'absen-in-22-1696984290.jpg', 'absen-out-22-1697018810.jpg', 1, '-4.8462742,104.9076611', '-4.8462149,104.907664', 'Ke rs dulu'),
  (180, 29, '2023-10-11', '00:00:00', '00:00:00', '', '', 2, '', '', ''),
  (181, 29, '2023-10-12', '00:00:00', '00:00:00', '', '', 2, '', '', ''),
  (182, 18, '2023-10-11', '11:30:11', '17:06:30', 'absen-in-18-1696998611.jpg', 'absen-out-18-1697018790.jpg', 1, '-4.846192282537381,104.90764848093762', '-4.8461538110184135,104.90759746809987', ''),
  (183, 30, '2023-10-11', '00:00:00', '00:00:00', '', '', 3, '', '', ''),
  (184, 30, '2023-10-12', '07:07:26', '17:02:25', 'absen-in-30-1697069246.jpg', 'absen-out-30-1697104945.jpg', 1, '-4.8398873,104.8862521', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403409,104.8865293</font></font>', ''),
  (185, 22, '2023-10-12', '07:08:36', '17:12:17', 'absen-in-22-1697069316.jpg', 'absen-out-22-1697105537.jpg', 1, '-4.8462066,104.9076424', '-4.84616,104.907581', ''),
  (186, 21, '2023-10-12', '07:10:01', '17:11:40', 'absen-in-21-1697069401.jpg', 'absen-out-21-1697105500.jpg', 1, '-4.8461972,104.9076491', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462064,104.9076637</font></font>', ''),
  (187, 17, '2023-10-12', '07:11:35', '17:04:39', 'absen-in-17-1697069495.jpg', 'absen-out-17-1697105079.jpg', 1, '-4.8401271,104.8865689', '-4.8401326,104.8865725', ''),
  (188, 20, '2023-10-12', '07:15:48', '17:02:59', 'absen-in-20-1697069748.jpg', 'absen-out-20-1697104979.jpg', 1, '-4.8401213,104.8865637', '-4.8402998,104.8865468', ''),
  (189, 19, '2023-10-12', '07:21:18', '17:11:05', 'absen-in-19-1697070078.jpg', 'absen-out-19-1697105465.jpg', 1, '-4.846283831231096,104.90765582970604', '-4.846300657633399,104.90768472022964', ''),
  (190, 28, '2023-10-12', '07:27:24', '17:04:06', 'absen-in-28-1697070444.jpg', 'absen-out-28-1697105046.jpg', 1, '-4.8401271,104.8865689', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403409,104.8865293</font></font>', ''),
  (191, 18, '2023-10-12', '07:39:22', '17:12:12', 'absen-in-18-1697071162.jpg', 'absen-out-18-1697105532.jpg', 1, '-4.846244555373773,104.9075354855318', '-4.846238524290034,104.90752576561921', ''),
  (192, 30, '2023-10-13', '07:05:19', '17:00:03', 'absen-in-30-1697155519.jpg', 'absen-out-30-1697191203.jpg', 1, '-4.8399155,104.8862668', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403478,104.8865315</font></font>', ''),
  (193, 29, '2023-10-13', '07:08:55', '17:00:22', 'absen-in-29-1697155735.jpg', 'absen-out-29-1697191222.jpg', 1, '-4.8400597,104.8863796', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403478,104.8865315</font></font>', ''),
  (194, 22, '2023-10-13', '07:11:18', '17:07:35', 'absen-in-22-1697155878.jpg', 'absen-out-22-1697191655.jpg', 1, '-4.846181,104.9076098', '-4.8462112,104.9076706', ''),
  (195, 20, '2023-10-13', '07:11:55', '00:00:00', 'absen-in-20-1697155915.jpg', '', 1, '-4.8400681,104.8863993', '', ''),
  (196, 21, '2023-10-13', '07:15:23', '17:06:00', 'absen-in-21-1697156123.jpg', 'absen-out-21-1697191560.jpg', 1, '-4.8462008,104.9076195', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462153,104.9076546</font></font>', ''),
  (197, 17, '2023-10-13', '07:16:33', '17:02:19', 'absen-in-17-1697156193.jpg', 'absen-out-17-1697191339.jpg', 1, '-4.8401336,104.8865736', '-4.8401215,104.8865739', ''),
  (198, 19, '2023-10-13', '07:20:56', '00:00:00', 'absen-in-19-1697156456.jpg', '', 1, '-4.846198504349998,104.90753151269251', '', ''),
  (199, 28, '2023-10-13', '07:21:35', '17:00:38', 'absen-in-28-1697156495.jpg', 'absen-out-28-1697191238.jpg', 1, '-4.8403506,104.8865347', '-4.84035,104.8865363', ''),
  (200, 18, '2023-10-13', '07:35:56', '17:06:42', 'absen-in-18-1697157356.jpg', 'absen-out-18-1697191602.jpg', 1, '-4.846243831047461,104.90764367480989', '-4.846130536673005,104.90767949853914', ''),
  (201, 30, '2023-10-14', '07:03:47', '16:59:15', 'absen-in-30-1697241827.jpg', 'absen-out-30-1697277555.jpg', 1, '-4.8400592,104.8866349', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840351,104.8865293</font></font>', ''),
  (202, 28, '2023-10-14', '07:06:47', '16:59:36', 'absen-in-28-1697242007.jpg', 'absen-out-28-1697277576.jpg', 1, '-4.8400592,104.8866349', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401887,104.8865614</font></font>', ''),
  (203, 29, '2023-10-14', '07:08:35', '16:59:01', 'absen-in-29-1697242115.jpg', 'absen-out-29-1697277541.jpg', 1, '-4.8401039,104.8865453', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403058,104.8865462</font></font>', ''),
  (204, 21, '2023-10-14', '07:10:47', '00:00:00', 'absen-in-21-1697242247.jpg', '', 1, '-4.8461982,104.9076539', '', ''),
  (205, 20, '2023-10-14', '07:11:54', '16:59:58', 'absen-in-20-1697242314.jpg', 'absen-out-20-1697277598.jpg', 1, '-4.8399628,104.8863002', '-4.8402386,104.8865823', ''),
  (206, 17, '2023-10-14', '07:12:05', '17:02:16', 'absen-in-17-1697242325.jpg', 'absen-out-17-1697277736.jpg', 1, '-4.8401053,104.8865532', '-4.8399988,104.8863356', ''),
  (207, 22, '2023-10-14', '07:16:41', '17:04:06', 'absen-in-22-1697242601.jpg', 'absen-out-22-1697277846.jpg', 1, '-4.8461951,104.9077561', '-4.8462109,104.9076684', ''),
  (208, 19, '2023-10-14', '07:19:31', '17:02:11', 'absen-in-19-1697242771.jpg', 'absen-out-19-1697277731.jpg', 1, '-4.846360286736683,104.90758728467345', '-4.846301796654801,104.90767233250516', ''),
  (209, 18, '2023-10-14', '07:29:08', '17:02:23', 'absen-in-18-1697243348.jpg', 'absen-out-18-1697277743.jpg', 1, '-4.8462090443689245,104.90765229954683', '-4.846107297139761,104.90769262297296', ''),
  (210, 30, '2023-10-16', '07:01:53', '17:01:18', 'absen-in-30-1697414513.jpg', 'absen-out-30-1697450478.jpg', 1, '-4.8400855,104.8865233', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401635,104.8865668</font></font>', ''),
  (211, 21, '2023-10-16', '07:08:45', '17:06:22', 'absen-in-21-1697414925.jpg', 'absen-out-21-1697450782.jpg', 1, '-4.8461928,104.9076289', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462077,104.9076655</font></font>', ''),
  (212, 22, '2023-10-16', '07:09:26', '17:06:02', 'absen-in-22-1697414966.jpg', 'absen-out-22-1697450762.jpg', 1, '-4.8462116,104.9076017', '-4.8462127,104.9076688', ''),
  (213, 20, '2023-10-16', '07:09:30', '17:02:42', 'absen-in-20-1697414970.jpg', 'absen-out-20-1697450562.jpg', 1, '-4.8401222,104.8865645', '-4.8399378,104.8866816', ''),
  (214, 29, '2023-10-16', '07:10:24', '17:02:46', 'absen-in-29-1697415024.jpg', 'absen-out-29-1697450566.jpg', 1, '-4.8400466,104.886389', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403525,104.8865303</font></font>', ''),
  (215, 28, '2023-10-16', '07:12:13', '17:02:17', 'absen-in-28-1697415133.jpg', 'absen-out-28-1697450537.jpg', 1, '-4.8402291,104.8865391', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401433,104.8865728</font></font>', ''),
  (216, 19, '2023-10-16', '07:21:20', '17:05:37', 'absen-in-19-1697415680.jpg', 'absen-out-19-1697450737.jpg', 1, '-4.84639842772444,104.90754673701828', '-4.846281887286448,104.90762941412966', ''),
  (217, 18, '2023-10-16', '08:25:58', '17:07:35', 'absen-in-18-1697419558.jpg', 'absen-out-18-1697450855.jpg', 1, '-4.846378317525385,104.90770767280407', '-4.846292309492983,104.90758492890875', 'Baru pulang dari balam'),
  (218, 17, '2023-10-16', '08:58:02', '17:06:39', 'absen-in-17-1697421482.jpg', 'absen-out-17-1697450799.jpg', 1, '-4.8401273,104.8865745', '-4.84013,104.8865759', ''),
  (219, 30, '2023-10-17', '07:02:52', '17:00:47', 'absen-in-30-1697500972.jpg', 'absen-out-30-1697536847.jpg', 1, '-4.8400193,104.8863159', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403493,104.8865371</font></font>', ''),
  (220, 21, '2023-10-17', '07:09:47', '17:06:04', 'absen-in-21-1697501387.jpg', 'absen-out-21-1697537164.jpg', 1, '-4.8461827,104.907631', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462133,104.9076513</font></font>', ''),
  (221, 20, '2023-10-17', '07:10:29', '17:03:04', 'absen-in-20-1697501429.jpg', 'absen-out-20-1697536984.jpg', 1, '-4.8401248,104.8865708', '-4.8402886,104.8865513', ''),
  (222, 28, '2023-10-17', '07:11:01', '17:01:41', 'absen-in-28-1697501461.jpg', 'absen-out-28-1697536901.jpg', 1, '-4.8399635,104.8862937', '-4.8403012,104.8865208', ''),
  (223, 29, '2023-10-17', '07:11:36', '17:00:17', 'absen-in-29-1697501496.jpg', 'absen-out-29-1697536817.jpg', 1, '-4.8401145,104.8865453', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401349,104.8865728</font></font>', ''),
  (224, 22, '2023-10-17', '07:14:27', '17:07:33', 'absen-in-22-1697501667.jpg', 'absen-out-22-1697537253.jpg', 1, '-4.8461253,104.9075588', '-4.8462125,104.9076726', ''),
  (225, 17, '2023-10-17', '07:16:43', '17:08:24', 'absen-in-17-1697501803.jpg', 'absen-out-17-1697537304.jpg', 1, '-4.8401121,104.886558', '-4.8399406,104.8868165', ''),
  (226, 19, '2023-10-17', '07:20:46', '17:05:13', 'absen-in-19-1697502046.jpg', 'absen-out-19-1697537113.jpg', 1, '-4.846205766509114,104.90746893582308', '-4.84630359110985,104.90768248704644', ''),
  (227, 18, '2023-10-17', '08:00:22', '17:06:12', 'absen-in-18-1697504422.jpg', 'absen-out-18-1697537172.jpg', 1, '-4.8462102,104.907662', '-4.8462089,104.9076663', 'Lupa absen'),
  (228, 30, '2023-10-18', '07:07:10', '17:00:46', 'absen-in-30-1697587630.jpg', 'absen-out-30-1697623246.jpg', 1, '-4.8402091,104.8866065', '-4.840337,104.8865898', ''),
  (229, 22, '2023-10-18', '07:09:52', '17:06:34', 'absen-in-22-1697587792.jpg', 'absen-out-22-1697623594.jpg', 1, '-4.8460226,104.9077347', '-4.8462108,104.9076703', ''),
  (230, 29, '2023-10-18', '07:10:09', '17:00:36', 'absen-in-29-1697587809.jpg', 'absen-out-29-1697623236.jpg', 1, '-4.8401234,104.8865734', '-4.8403531,104.8865305', ''),
  (231, 20, '2023-10-18', '07:11:38', '17:01:20', 'absen-in-20-1697587898.jpg', 'absen-out-20-1697623280.jpg', 1, '-4.8401138,104.8865664', '-4.84029,104.88655', ''),
  (232, 21, '2023-10-18', '07:14:32', '17:04:17', 'absen-in-21-1697588072.jpg', 'absen-out-21-1697623457.jpg', 1, '-4.8461872,104.9076287', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8462123,104.9076528</font></font>', ''),
  (233, 28, '2023-10-18', '07:17:33', '17:01:17', 'absen-in-28-1697588253.jpg', 'absen-out-28-1697623277.jpg', 1, '-4.8402091,104.8866065', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840337,104.8865898</font></font>', ''),
  (234, 17, '2023-10-18', '07:18:30', '17:03:06', 'absen-in-17-1697588310.jpg', 'absen-out-17-1697623386.jpg', 1, '-4.8401247,104.8865709', '-4.8401306,104.8865759', ''),
  (235, 19, '2023-10-18', '07:23:24', '17:04:31', 'absen-in-19-1697588604.jpg', 'absen-out-19-1697623471.jpg', 1, '-4.846293284291044,104.90765561639112', '-4.846156507847403,104.90755210175362', ''),
  (236, 18, '2023-10-18', '07:30:33', '17:04:57', 'absen-in-18-1697589033.jpg', 'absen-out-18-1697623497.jpg', 1, '-4.8462019,104.9076629', '-4.8462133,104.9076682', ''),
  (237, 22, '2023-10-19', '07:01:43', '17:06:45', 'absen-in-22-1697673703.jpg', 'absen-out-22-1697710005.jpg', 1, '-4.8461151,104.9076192', '-4.8462158,104.9076641', ''),
  (238, 30, '2023-10-19', '07:01:59', '16:59:53', 'absen-in-30-1697673719.jpg', 'absen-out-30-1697709593.jpg', 1, '-4.8402275,104.886461', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403688,104.8865089</font></font>', ''),
  (239, 21, '2023-10-19', '07:11:23', '17:04:48', 'absen-in-21-1697674283.jpg', 'absen-out-21-1697709888.jpg', 1, '-4.8462019,104.9076476', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8461192,104.9075364</font></font>', ''),
  (240, 20, '2023-10-19', '07:12:51', '17:00:08', 'absen-in-20-1697674371.jpg', 'absen-out-20-1697709608.jpg', 1, '-4.8401229,104.8865704', '-4.8401315,104.8865752', ''),
  (241, 28, '2023-10-19', '07:13:35', '17:00:30', 'absen-in-28-1697674415.jpg', 'absen-out-28-1697709630.jpg', 1, '-4.8399333,104.8863059', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8402067,104.8865967</font></font>', ''),
  (242, 29, '2023-10-19', '07:15:50', '16:59:24', 'absen-in-29-1697674550.jpg', 'absen-out-29-1697709564.jpg', 1, '-4.8400306,104.8864414', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840134,104.8865754</font></font>', ''),
  (243, 17, '2023-10-19', '07:16:57', '17:01:52', 'absen-in-17-1697674617.jpg', 'absen-out-17-1697709712.jpg', 1, '-4.8401132,104.8865575', '-4.8403506,104.8865347', ''),
  (244, 19, '2023-10-19', '07:20:15', '17:04:40', 'absen-in-19-1697674815.jpg', 'absen-out-19-1697709880.jpg', 1, '-4.8461425420890185,104.90738736632751', '-4.846292155696094,104.90765376613973', ''),
  (245, 18, '2023-10-19', '07:32:54', '17:05:52', 'absen-in-18-1697675574.jpg', 'absen-out-18-1697709952.jpg', 1, '-4.8462018,104.9076537', '-4.8462133,104.9076617', ''),
  (246, 30, '2023-10-20', '06:59:04', '17:00:31', 'absen-in-30-1697759944.jpg', 'absen-out-30-1697796031.jpg', 1, '-4.8401033,104.8865224', '-4.8400961,104.8864094', ''),
  (247, 29, '2023-10-20', '07:03:05', '16:59:13', 'absen-in-29-1697760185.jpg', 'absen-out-29-1697795953.jpg', 1, '-4.8401197,104.8865699', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403245,104.8864894</font></font>', ''),
  (248, 28, '2023-10-20', '07:07:50', '17:01:30', 'absen-in-28-1697760470.jpg', 'absen-out-28-1697796090.jpg', 1, '-4.8400941,104.8864963', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8400961,104.8864094</font></font>', ''),
  (249, 21, '2023-10-20', '07:08:05', '00:00:00', 'absen-in-21-1697760485.jpg', '', 1, '-4.846205,104.9076494', '', ''),
  (250, 22, '2023-10-20', '07:10:05', '17:06:03', 'absen-in-22-1697760605.jpg', 'absen-out-22-1697796363.jpg', 1, '-4.8461087,104.9075601', '-4.8462197,104.907668', ''),
  (251, 20, '2023-10-20', '07:11:50', '17:00:20', 'absen-in-20-1697760710.jpg', 'absen-out-20-1697796020.jpg', 1, '-4.8401171,104.8865683', '-4.8401544,104.8864981', ''),
  (252, 17, '2023-10-20', '07:14:08', '17:03:01', 'absen-in-17-1697760848.jpg', 'absen-out-17-1697796181.jpg', 1, '-4.8400773,104.8864947', '-4.8401379,104.8865743', ''),
  (253, 19, '2023-10-20', '07:19:36', '17:03:53', 'absen-in-19-1697761176.jpg', 'absen-out-19-1697796233.jpg', 1, '-4.846223885054911,104.90753145746753', '-4.8462893305091725,104.9076487467297', ''),
  (254, 18, '2023-10-20', '07:25:03', '17:04:18', 'absen-in-18-1697761503.jpg', 'absen-out-18-1697796258.jpg', 1, '-4.8462163,104.9076643', '-4.8462193,104.9076657', ''),
  (255, 30, '2023-10-21', '07:10:16', '17:00:42', 'absen-in-30-1697847016.jpg', 'absen-out-30-1697882442.jpg', 1, '-4.8398758,104.8862397', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403561,104.8865028</font></font>', ''),
  (256, 28, '2023-10-21', '07:10:35', '17:01:23', 'absen-in-28-1697847035.jpg', 'absen-out-28-1697882483.jpg', 1, '-4.8398758,104.8862397', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403688,104.886506</font></font>', ''),
  (257, 20, '2023-10-21', '07:12:16', '17:01:37', 'absen-in-20-1697847136.jpg', 'absen-out-20-1697882497.jpg', 1, '-4.8401251,104.8865688', '-4.8401285,104.8865704', ''),
  (258, 17, '2023-10-21', '07:13:47', '17:02:40', 'absen-in-17-1697847227.jpg', 'absen-out-17-1697882560.jpg', 1, '-4.8400652,104.88649', '-4.8401285,104.8865758', ''),
  (259, 19, '2023-10-21', '07:16:24', '17:02:52', 'absen-in-19-1697847384.jpg', 'absen-out-19-1697882572.jpg', 1, '-4.846275911094682,104.90757936439682', '-4.84629352070588,104.9076580751243', '');
  INSERT INTO `presence` (`presence_id`, `employees_id`, `presence_date`, `time_in`, `time_out`, `picture_in`, `picture_out`, `present_id`, `latitude_longtitude_in`, `latitude_longtitude_out`, `information`) VALUES
  (260, 22, '2023-10-21', '07:21:28', '17:03:41', 'absen-in-22-1697847688.jpg', 'absen-out-22-1697882621.jpg', 1, '-4.8462254,104.9076436', '-4.8462188,104.9076648', ''),
  (261, 18, '2023-10-21', '07:23:49', '17:02:10', 'absen-in-18-1697847829.jpg', 'absen-out-18-1697882530.jpg', 1, '-4.8462123,104.9076662', '-4.8462232,104.9076653', ''),
  (262, 29, '2023-10-21', '07:30:18', '00:00:00', 'absen-in-29-1697848218.jpg', '', 1, '-4.8403516,104.8865268', '', ''),
  (263, 22, '2023-10-23', '07:03:28', '17:06:00', 'absen-in-22-1698019408.jpg', 'absen-out-22-1698055560.jpg', 1, '-4.8461909,104.9076568', '-4.8462193,104.9076667', ''),
  (264, 30, '2023-10-23', '07:03:36', '17:00:48', 'absen-in-30-1698019416.jpg', 'absen-out-30-1698055248.jpg', 1, '-4.8400467,104.8863361', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403512,104.8864811</font></font>', ''),
  (265, 28, '2023-10-23', '07:11:54', '17:01:21', 'absen-in-28-1698019914.jpg', 'absen-out-28-1698055281.jpg', 1, '-4.8400467,104.8863361', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403699,104.8865189</font></font>', ''),
  (266, 20, '2023-10-23', '07:13:18', '17:01:13', 'absen-in-20-1698019998.jpg', 'absen-out-20-1698055273.jpg', 1, '-4.8401153,104.8865678', '-4.8403638,104.8865123', ''),
  (267, 17, '2023-10-23', '07:13:26', '17:02:13', 'absen-in-17-1698020006.jpg', 'absen-out-17-1698055333.jpg', 1, '-4.8401128,104.8865519', '-4.8403254,104.8864989', ''),
  (268, 29, '2023-10-23', '07:16:30', '16:59:21', 'absen-in-29-1698020190.jpg', 'absen-out-29-1698055161.jpg', 1, '-4.840365,104.8865123', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401399,104.8865714</font></font>', ''),
  (269, 19, '2023-10-23', '07:17:02', '17:07:08', 'absen-in-19-1698020222.jpg', 'absen-out-19-1698055628.jpg', 1, '-4.846325398141303,104.90762778038518', '-4.846301527747314,104.90767567907388', ''),
  (270, 18, '2023-10-23', '07:26:28', '17:04:32', 'absen-in-18-1698020788.jpg', 'absen-out-18-1698055472.jpg', 1, '-4.8462071,104.9076564', '-4.8462183,104.9076646', ''),
  (271, 30, '2023-10-24', '06:55:47', '16:59:35', 'absen-in-30-1698105347.jpg', 'absen-out-30-1698141575.jpg', 1, '-4.8400932,104.8865045', '-4.8401359,104.8865739', ''),
  (272, 22, '2023-10-24', '07:03:59', '17:04:09', 'absen-in-22-1698105839.jpg', 'absen-out-22-1698141849.jpg', 1, '-4.8461735,104.9076818', '-4.8462102,104.9076702', ''),
  (273, 28, '2023-10-24', '07:07:03', '17:00:27', 'absen-in-28-1698106023.jpg', 'absen-out-28-1698141627.jpg', 1, '-4.8401054,104.8865414', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401359,104.8865739</font></font>', ''),
  (274, 29, '2023-10-24', '07:15:06', '16:59:25', 'absen-in-29-1698106506.jpg', 'absen-out-29-1698141565.jpg', 1, '-4.8401177,104.8865633', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401364,104.8865734</font></font>', ''),
  (275, 17, '2023-10-24', '07:15:57', '17:02:30', 'absen-in-17-1698106557.jpg', 'absen-out-17-1698141750.jpg', 1, '-4.8400817,104.8864989', '-4.8401316,104.8865666', ''),
  (276, 19, '2023-10-24', '07:16:46', '17:03:48', 'absen-in-19-1698106606.jpg', 'absen-out-19-1698141828.jpg', 1, '-4.846282847863941,104.90759100918933', '-4.846308055940124,104.90763427648146', ''),
  (277, 20, '2023-10-24', '07:17:31', '17:01:28', 'absen-in-20-1698106651.jpg', 'absen-out-20-1698141688.jpg', 1, '-4.8402115,104.8865649', '-4.8401356,104.886572', ''),
  (278, 18, '2023-10-24', '07:35:03', '17:05:46', 'absen-in-18-1698107703.jpg', 'absen-out-18-1698141946.jpg', 1, '-4.8462246,104.9076647', '-4.8462085,104.9076585', ''),
  (279, 20, '2023-10-25', '07:08:36', '17:01:06', 'absen-in-20-1698192516.jpg', 'absen-out-20-1698228066.jpg', 1, '-4.8401151,104.8865665', '-4.8401153,104.8865347', ''),
  (280, 30, '2023-10-25', '07:10:07', '16:59:47', 'absen-in-30-1698192607.jpg', 'absen-out-30-1698227987.jpg', 1, '-4.8399907,104.8862797', '-4.8403632,104.8865064', ''),
  (281, 22, '2023-10-25', '07:13:49', '17:02:45', 'absen-in-22-1698192829.jpg', 'absen-out-22-1698228165.jpg', 1, '-4.8461707,104.9076741', '-4.8462132,104.9076692', ''),
  (282, 29, '2023-10-25', '07:13:56', '16:59:09', 'absen-in-29-1698192836.jpg', 'absen-out-29-1698227949.jpg', 1, '-4.8401209,104.8865632', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401347,104.8865722</font></font>', ''),
  (283, 17, '2023-10-25', '07:14:00', '17:01:55', 'absen-in-17-1698192840.jpg', 'absen-out-17-1698228115.jpg', 1, '-4.840124,104.8865641', '-4.8401296,104.8865717', ''),
  (284, 28, '2023-10-25', '07:14:25', '17:01:09', 'absen-in-28-1698192865.jpg', 'absen-out-28-1698228069.jpg', 1, '-4.840038,104.8863373', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403632,104.8865064</font></font>', ''),
  (285, 19, '2023-10-25', '07:18:43', '17:02:27', 'absen-in-19-1698193123.jpg', 'absen-out-19-1698228147.jpg', 1, '-4.846300766144033,104.9076269776008', '-4.846276220633404,104.90764455192219', ''),
  (286, 18, '2023-10-25', '07:32:50', '00:00:00', 'absen-in-18-1698193970.jpg', '', 1, '-4.846214,104.9076682', '', ''),
  (287, 30, '2023-10-26', '07:03:40', '17:00:48', 'absen-in-30-1698278620.jpg', 'absen-out-30-1698314448.jpg', 1, '-4.8400712,104.8864682', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401306,104.8865718</font></font>', ''),
  (288, 20, '2023-10-26', '07:07:52', '17:01:37', 'absen-in-20-1698278872.jpg', 'absen-out-20-1698314497.jpg', 1, '-4.8401141,104.8865499', '-4.8400604,104.8866455', ''),
  (289, 29, '2023-10-26', '07:11:03', '16:59:42', 'absen-in-29-1698279063.jpg', 'absen-out-29-1698314382.jpg', 1, '-4.8401163,104.8865571', '-4.8401387,104.8865717', ''),
  (290, 28, '2023-10-26', '07:15:20', '17:01:07', 'absen-in-28-1698279320.jpg', 'absen-out-28-1698314467.jpg', 1, '-4.8400712,104.8864682', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401306,104.8865718</font></font>', ''),
  (291, 19, '2023-10-26', '07:18:10', '17:03:38', 'absen-in-19-1698279490.jpg', 'absen-out-19-1698314618.jpg', 1, '-4.84618198350725,104.90742851524344', '-4.8461634086941325,104.90772623972269', ''),
  (292, 17, '2023-10-26', '07:18:13', '17:02:50', 'absen-in-17-1698279493.jpg', 'absen-out-17-1698314570.jpg', 1, '-4.8401259,104.8865663', '-4.840265,104.8866617', ''),
  (293, 22, '2023-10-26', '07:26:26', '17:05:40', 'absen-in-22-1698279986.jpg', 'absen-out-22-1698314740.jpg', 1, '-4.8461936,104.9077159', '-4.8462119,104.9076704', 'Nganter kakak dulu'),
  (294, 18, '2023-10-26', '17:03:51', '17:04:03', 'absen-in-18-1698314631.jpg', 'absen-out-18-1698314643.jpg', 1, '-4.8462123,104.9076692', '-4.8462123,104.9076692', ''),
  (295, 29, '2023-10-27', '07:09:52', '16:59:11', 'absen-in-29-1698365392.jpg', 'absen-out-29-1698400751.jpg', 1, '-4.8401061,104.8865514', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401363,104.8865701</font></font>', ''),
  (296, 30, '2023-10-27', '07:10:09', '17:00:46', 'absen-in-30-1698365409.jpg', 'absen-out-30-1698400846.jpg', 1, '-4.8400623,104.8864486', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401314,104.8865732</font></font>', ''),
  (297, 28, '2023-10-27', '07:14:01', '17:00:44', 'absen-in-28-1698365641.jpg', 'absen-out-28-1698400844.jpg', 1, '-4.8400623,104.8864486', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401327,104.8865726</font></font>', ''),
  (298, 17, '2023-10-27', '07:14:48', '17:06:09', 'absen-in-17-1698365688.jpg', 'absen-out-17-1698401169.jpg', 1, '-4.8401274,104.8865517', '-4.8400817,104.8866098', ''),
  (299, 19, '2023-10-27', '07:20:36', '17:03:11', 'absen-in-19-1698366036.jpg', 'absen-out-19-1698400991.jpg', 1, '-4.846157250536179,104.90745934510784', '-4.84630142025896,104.90760834593227', ''),
  (300, 22, '2023-10-27', '07:23:56', '17:01:53', 'absen-in-22-1698366236.jpg', 'absen-out-22-1698400913.jpg', 1, '-4.8461967,104.90759', '-4.8462107,104.9076683', ''),
  (301, 20, '2023-10-27', '07:24:21', '00:00:00', 'absen-in-20-1698366261.jpg', '', 1, '-4.8400913,104.8864092', '', ''),
  (302, 18, '2023-10-27', '07:32:02', '17:02:04', 'absen-in-18-1698366722.jpg', 'absen-out-18-1698400924.jpg', 1, '-4.8462088,104.9076684', '-4.8462109,104.9076669', ''),
  (303, 29, '2023-10-28', '07:08:16', '16:58:56', 'absen-in-29-1698451696.jpg', 'absen-out-29-1698487136.jpg', 1, '-4.840076,104.8864677', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401337,104.8865732</font></font>', ''),
  (304, 22, '2023-10-28', '07:09:42', '17:02:23', 'absen-in-22-1698451782.jpg', 'absen-out-22-1698487343.jpg', 1, '-4.8461983,104.9075968', '-4.8462075,104.9076697', ''),
  (305, 30, '2023-10-28', '07:10:23', '17:00:30', 'absen-in-30-1698451823.jpg', 'absen-out-30-1698487230.jpg', 1, '-4.8401091,104.8865382', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403017,104.886496</font></font>', ''),
  (306, 20, '2023-10-28', '07:10:46', '17:01:24', 'absen-in-20-1698451846.jpg', 'absen-out-20-1698487284.jpg', 1, '-4.8400695,104.8864897', '-4.8401202,104.8865672', ''),
  (307, 17, '2023-10-28', '07:11:59', '17:02:38', 'absen-in-17-1698451919.jpg', 'absen-out-17-1698487358.jpg', 1, '-4.840116,104.8865489', '-4.8399906,104.8862909', ''),
  (308, 28, '2023-10-28', '07:16:31', '17:00:53', 'absen-in-28-1698452191.jpg', 'absen-out-28-1698487253.jpg', 1, '-4.8401234,104.8865738', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403017,104.886496</font></font>', ''),
  (309, 19, '2023-10-28', '07:22:24', '17:00:11', 'absen-in-19-1698452544.jpg', 'absen-out-19-1698487211.jpg', 1, '-4.8462795365621885,104.90764863662142', '-4.846313766814894,104.9076461842583', ''),
  (310, 18, '2023-10-28', '17:00:44', '17:00:53', 'absen-in-18-1698487244.jpg', 'absen-out-18-1698487253.jpg', 1, '-4.8462098,104.9076673', '-4.8462098,104.9076673', ''),
  (311, 29, '2023-10-30', '07:06:05', '17:01:41', 'absen-in-29-1698624365.jpg', 'absen-out-29-1698660101.jpg', 1, '-4.8401293,104.8865674', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401438,104.8865649</font></font>', ''),
  (312, 30, '2023-10-30', '07:06:49', '17:00:46', 'absen-in-30-1698624409.jpg', 'absen-out-30-1698660046.jpg', 1, '-4.840076,104.8864665', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8402968,104.8864938</font></font>', ''),
  (313, 28, '2023-10-30', '07:07:37', '17:00:36', 'absen-in-28-1698624457.jpg', 'absen-out-28-1698660036.jpg', 1, '-4.840076,104.8864665', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840136,104.8865724</font></font>', ''),
  (314, 17, '2023-10-30', '07:13:33', '17:03:54', 'absen-in-17-1698624813.jpg', 'absen-out-17-1698660234.jpg', 1, '-4.8401208,104.8865554', '-4.840131,104.886573', ''),
  (315, 22, '2023-10-30', '07:14:39', '17:04:38', 'absen-in-22-1698624879.jpg', 'absen-out-22-1698660278.jpg', 1, '-4.846166,104.90764', '-4.8462102,104.9076692', ''),
  (316, 19, '2023-10-30', '07:18:17', '17:02:43', 'absen-in-19-1698625097.jpg', 'absen-out-19-1698660163.jpg', 1, '-4.846272181597687,104.90757295616939', '-4.846288542939001,104.90767251161779', ''),
  (317, 20, '2023-10-30', '07:19:22', '17:01:53', 'absen-in-20-1698625162.jpg', 'absen-out-20-1698660113.jpg', 1, '-4.8402841,104.8864593', '-4.8401987,104.886542', ''),
  (318, 18, '2023-10-30', '07:28:23', '17:03:03', 'absen-in-18-1698625703.jpg', 'absen-out-18-1698660183.jpg', 1, '-4.8462059,104.9076639', '-4.8462107,104.9076643', ''),
  (319, 30, '2023-10-31', '06:59:55', '16:59:52', 'absen-in-30-1698710395.jpg', 'absen-out-30-1698746392.jpg', 1, '-4.8400234,104.8863169', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401317,104.886571</font></font>', ''),
  (320, 29, '2023-10-31', '07:06:26', '16:59:05', 'absen-in-29-1698710786.jpg', 'absen-out-29-1698746345.jpg', 1, '-4.840115,104.88655', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401366,104.8865726</font></font>', ''),
  (321, 22, '2023-10-31', '07:06:49', '17:08:12', 'absen-in-22-1698710809.jpg', 'absen-out-22-1698746892.jpg', 1, '-4.8461889,104.9076311', '-4.8462349,104.9076667', ''),
  (322, 20, '2023-10-31', '07:10:10', '17:02:09', 'absen-in-20-1698711010.jpg', 'absen-out-20-1698746529.jpg', 1, '-4.8401086,104.8865516', '-4.8401256,104.886571', ''),
  (323, 28, '2023-10-31', '07:13:01', '17:00:14', 'absen-in-28-1698711181.jpg', 'absen-out-28-1698746414.jpg', 1, '-4.8401201,104.8865597', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403638,104.8865044</font></font>', ''),
  (324, 17, '2023-10-31', '07:13:29', '17:03:40', 'absen-in-17-1698711209.jpg', 'absen-out-17-1698746620.jpg', 1, '-4.840117,104.8865413', '-4.8403638,104.8865044', ''),
  (325, 19, '2023-10-31', '07:19:20', '17:07:07', 'absen-in-19-1698711560.jpg', 'absen-out-19-1698746827.jpg', 1, '-4.846171742160993,104.90763174953979', '-4.846270558813182,104.90763699047851', ''),
  (326, 18, '2023-10-31', '07:29:06', '17:06:44', 'absen-in-18-1698712146.jpg', 'absen-out-18-1698746804.jpg', 1, '-4.8462371,104.9076954', '-4.8462119,104.9076685', ''),
  (327, 17, '2023-11-01', '07:12:14', '17:01:31', 'absen-in-17-1698797534.jpg', 'absen-out-17-1698832891.jpg', 1, '-4.8401045,104.8865226', '-4.8400749,104.8867068', ''),
  (328, 30, '2023-11-01', '07:13:09', '16:59:58', 'absen-in-30-1698797589.jpg', 'absen-out-30-1698832798.jpg', 1, '-4.8400797,104.8865027', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401364,104.8865728</font></font>', ''),
  (329, 28, '2023-11-01', '07:13:45', '17:00:33', 'absen-in-28-1698797625.jpg', 'absen-out-28-1698832833.jpg', 1, '-4.8401089,104.8865489', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401364,104.8865728</font></font>', ''),
  (330, 20, '2023-11-01', '07:14:19', '17:01:21', 'absen-in-20-1698797659.jpg', 'absen-out-20-1698832881.jpg', 1, '-4.8401162,104.8865203', '-4.8401259,104.8865729', ''),
  (331, 29, '2023-11-01', '07:17:30', '16:58:16', 'absen-in-29-1698797850.jpg', 'absen-out-29-1698832696.jpg', 1, '-4.8399773,104.8863062', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8402293,104.8864268</font></font>', ''),
  (332, 19, '2023-11-01', '07:18:41', '17:06:05', 'absen-in-19-1698797921.jpg', 'absen-out-19-1698833165.jpg', 1, '-4.846235416529262,104.9075063878227', '-4.846270936693671,104.90762404238069', ''),
  (333, 22, '2023-11-01', '07:24:18', '17:07:56', 'absen-in-22-1698798258.jpg', 'absen-out-22-1698833276.jpg', 1, '-4.8461988,104.9077053', '-4.8462147,104.9076686', ''),
  (334, 18, '2023-11-01', '07:25:01', '17:06:56', 'absen-in-18-1698798301.jpg', 'absen-out-18-1698833216.jpg', 1, '-4.8462077,104.9076682', '-4.8462113,104.9076686', ''),
  (335, 30, '2023-11-02', '07:10:31', '17:01:43', 'absen-in-30-1698883831.jpg', 'absen-out-30-1698919303.jpg', 1, '-4.8399118,104.8862193', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401335,104.8865742</font></font>', ''),
  (336, 20, '2023-11-02', '07:12:29', '17:02:53', 'absen-in-20-1698883949.jpg', 'absen-out-20-1698919373.jpg', 1, '-4.8400721,104.886411', '-4.8401324,104.8865671', ''),
  (337, 17, '2023-11-02', '07:14:16', '17:05:55', 'absen-in-17-1698884056.jpg', 'absen-out-17-1698919555.jpg', 1, '-4.8401022,104.8865259', '-4.8401324,104.8865671', ''),
  (338, 29, '2023-11-02', '07:14:42', '16:59:47', 'absen-in-29-1698884082.jpg', 'absen-out-29-1698919187.jpg', 1, '-4.8400411,104.8863104', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401321,104.8865669</font></font>', ''),
  (339, 28, '2023-11-02', '07:15:28', '17:02:10', 'absen-in-28-1698884128.jpg', 'absen-out-28-1698919330.jpg', 1, '-4.8399581,104.8862783', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401335,104.8865742</font></font>', ''),
  (340, 22, '2023-11-02', '07:21:22', '17:06:06', 'absen-in-22-1698884482.jpg', 'absen-out-22-1698919566.jpg', 1, '-4.8461438,104.9076506', '-4.8462109,104.9076684', ''),
  (341, 19, '2023-11-02', '07:22:14', '17:04:17', 'absen-in-19-1698884534.jpg', 'absen-out-19-1698919457.jpg', 1, '-4.846264513822189,104.90751009357676', '-4.8463056967971605,104.9076242169957', ''),
  (342, 18, '2023-11-02', '07:31:10', '17:04:28', 'absen-in-18-1698885070.jpg', 'absen-out-18-1698919468.jpg', 1, '-4.8462109,104.9076653', '-4.8462095,104.9076673', ''),
  (343, 30, '2023-11-03', '07:07:26', '17:00:31', 'absen-in-30-1698970046.jpg', 'absen-out-30-1699005631.jpg', 1, '-4.8401319,104.8865396', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401363,104.886569</font></font>', ''),
  (344, 20, '2023-11-03', '07:14:01', '17:01:21', 'absen-in-20-1698970441.jpg', 'absen-out-20-1699005681.jpg', 1, '-4.8399157,104.886238', '-4.8403814,104.8865326', ''),
  (345, 29, '2023-11-03', '07:14:20', '16:59:37', 'absen-in-29-1698970460.jpg', 'absen-out-29-1699005577.jpg', 1, '-4.8401184,104.8865623', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401449,104.8865644</font></font>', ''),
  (346, 28, '2023-11-03', '07:17:59', '17:01:05', 'absen-in-28-1698970679.jpg', 'absen-out-28-1699005665.jpg', 1, '-4.8400808,104.8864837', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401363,104.886569</font></font>', ''),
  (347, 19, '2023-11-03', '07:18:39', '17:02:47', 'absen-in-19-1698970719.jpg', 'absen-out-19-1699005767.jpg', 1, '-4.846263704175187,104.90751709605183', '-4.8462706600038885,104.90763703872584', ''),
  (348, 17, '2023-11-03', '07:18:44', '17:02:55', 'absen-in-17-1698970724.jpg', 'absen-out-17-1699005775.jpg', 1, '-4.8401156,104.8865523', '-4.840134,104.8865687', ''),
  (349, 18, '2023-11-03', '07:29:47', '17:03:12', 'absen-in-18-1698971387.jpg', 'absen-out-18-1699005792.jpg', 1, '-4.8462083,104.9076666', '-4.8462123,104.9076682', ''),
  (350, 22, '2023-11-03', '07:30:14', '17:04:22', 'absen-in-22-1698971414.jpg', 'absen-out-22-1699005862.jpg', 1, '-4.8461836,104.907648', '-4.8462099,104.9076709', ''),
  (351, 29, '2023-11-04', '07:08:26', '16:59:50', 'absen-in-29-1699056506.jpg', 'absen-out-29-1699091990.jpg', 1, '-4.8401146,104.8865535', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401326,104.8865703</font></font>', ''),
  (352, 30, '2023-11-04', '07:10:16', '17:00:22', 'absen-in-30-1699056616.jpg', 'absen-out-30-1699092022.jpg', 1, '-4.8401083,104.8865531', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8402247,104.8865575</font></font>', ''),
  (353, 28, '2023-11-04', '07:11:58', '17:00:36', 'absen-in-28-1699056718.jpg', 'absen-out-28-1699092036.jpg', 1, '-4.8401129,104.8865301', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8402981,104.8864906</font></font>', ''),
  (354, 17, '2023-11-04', '07:15:08', '17:02:28', 'absen-in-17-1699056908.jpg', 'absen-out-17-1699092148.jpg', 1, '-4.8401067,104.8865411', '-4.8403292,104.8865092', ''),
  (355, 20, '2023-11-04', '07:15:24', '17:02:07', 'absen-in-20-1699056924.jpg', 'absen-out-20-1699092127.jpg', 1, '-4.8399977,104.8863079', '-4.8401287,104.8865792', ''),
  (356, 19, '2023-11-04', '07:19:58', '16:31:43', 'absen-in-19-1699057198.jpg', 'absen-out-19-1699090303.jpg', 1, '-4.8462020286872,104.90741501969993', '-4.846125399023478,104.90735710444842', 'Izin pulang mau jalan ke gendot'),
  (357, 18, '2023-11-04', '07:27:28', '17:09:15', 'absen-in-18-1699057648.jpg', 'absen-out-18-1699092555.jpg', 1, '-4.8461919,104.907666', '-4.8461824,104.9076324', ''),
  (358, 29, '2023-11-06', '07:06:12', '16:59:17', 'absen-in-29-1699229172.jpg', 'absen-out-29-1699264757.jpg', 1, '-4.8400685,104.8864747', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401407,104.8865691</font></font>', ''),
  (359, 30, '2023-11-06', '07:09:37', '16:59:45', 'absen-in-30-1699229377.jpg', 'absen-out-30-1699264785.jpg', 1, '-4.8400542,104.8863781', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401713,104.886562</font></font>', ''),
  (360, 20, '2023-11-06', '07:12:24', '17:01:34', 'absen-in-20-1699229544.jpg', 'absen-out-20-1699264894.jpg', 1, '-4.8400816,104.8864768', '-4.8401557,104.8865626', ''),
  (361, 17, '2023-11-06', '07:14:14', '17:01:47', 'absen-in-17-1699229654.jpg', 'absen-out-17-1699264907.jpg', 1, '-4.8401317,104.8865695', '-4.8401319,104.8865701', ''),
  (362, 18, '2023-11-06', '07:19:01', '17:08:32', 'absen-in-18-1699229941.jpg', 'absen-out-18-1699265312.jpg', 1, '-4.8461888,104.9076394', '-4.8462986,104.9076454', ''),
  (363, 19, '2023-11-06', '07:26:04', '17:07:12', 'absen-in-19-1699230364.jpg', 'absen-out-19-1699265232.jpg', 1, '-4.846223296125682,104.90750731108191', '-4.846293864768823,104.9076659832634', ''),
  (364, 28, '2023-11-06', '00:00:00', '00:00:00', '', '', 3, '', '', ''),
  (365, 20, '2023-11-07', '07:07:37', '17:00:51', 'absen-in-20-1699315657.jpg', 'absen-out-20-1699351251.jpg', 1, '-4.8400804,104.8864988', '-4.8403821,104.8865291', ''),
  (366, 30, '2023-11-07', '07:09:12', '00:00:00', 'absen-in-30-1699315752.jpg', '', 1, '-4.8401037,104.8865325', '', ''),
  (367, 29, '2023-11-07', '07:13:14', '16:59:02', 'absen-in-29-1699315994.jpg', 'absen-out-29-1699351142.jpg', 1, '-4.8401235,104.8865368', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401478,104.8865638</font></font>', ''),
  (368, 17, '2023-11-07', '07:17:17', '17:04:23', 'absen-in-17-1699316237.jpg', 'absen-out-17-1699351463.jpg', 1, '-4.8401209,104.8865578', '-4.8403138,104.8864989', ''),
  (369, 28, '2023-11-07', '07:20:11', '17:00:35', 'absen-in-28-1699316411.jpg', 'absen-out-28-1699351235.jpg', 1, '-4.8401037,104.8865325', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401334,104.8865681</font></font>', ''),
  (370, 19, '2023-11-07', '07:23:22', '17:02:55', 'absen-in-19-1699316602.jpg', 'absen-out-19-1699351375.jpg', 1, '-4.846320194280922,104.90754363715581', '-4.8462646045293525,104.9075631395311', ''),
  (371, 18, '2023-11-07', '07:35:46', '17:00:44', 'absen-in-18-1699317346.jpg', 'absen-out-18-1699351244.jpg', 1, '-4.8462116,104.9076699', '-4.8462106,104.9076699', ''),
  (372, 29, '2023-11-08', '07:07:57', '16:59:45', 'absen-in-29-1699402077.jpg', 'absen-out-29-1699437585.jpg', 1, '-4.8401081,104.8865289', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401454,104.8865679</font></font>', ''),
  (373, 30, '2023-11-08', '07:10:25', '17:00:59', 'absen-in-30-1699402225.jpg', 'absen-out-30-1699437659.jpg', 1, '-4.8401025,104.8865294', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403091,104.8865435</font></font>', ''),
  (374, 20, '2023-11-08', '07:13:10', '17:01:47', 'absen-in-20-1699402390.jpg', 'absen-out-20-1699437707.jpg', 1, '-4.8401265,104.8865658', '-4.840382,104.8865281', ''),
  (375, 28, '2023-11-08', '07:14:31', '17:01:22', 'absen-in-28-1699402471.jpg', 'absen-out-28-1699437682.jpg', 1, '-4.8399094,104.8863627', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401945,104.8865783</font></font>', ''),
  (376, 17, '2023-11-08', '07:18:59', '17:02:27', 'absen-in-17-1699402739.jpg', 'absen-out-17-1699437747.jpg', 1, '-4.8401078,104.886537', '-4.8401318,104.8865567', ''),
  (377, 19, '2023-11-08', '07:22:02', '17:05:31', 'absen-in-19-1699402922.jpg', 'absen-out-19-1699437931.jpg', 1, '-4.846336192683126,104.90757481497995', '-4.846310747751258,104.90760768809173', ''),
  (378, 18, '2023-11-08', '07:36:53', '17:03:36', 'absen-in-18-1699403813.jpg', 'absen-out-18-1699437816.jpg', 1, '-4.8462106,104.90767', '-4.8462114,104.9076699', ''),
  (379, 30, '2023-11-09', '07:06:12', '00:00:00', 'absen-in-30-1699488372.jpg', '', 1, '-4.8400816,104.8865244', '', ''),
  (380, 20, '2023-11-09', '07:08:23', '00:00:00', 'absen-in-20-1699488503.jpg', '', 1, '-4.8401023,104.8865571', '', ''),
  (381, 28, '2023-11-09', '07:15:43', '00:00:00', 'absen-in-28-1699488943.jpg', '', 1, '-4.840051,104.8866038', '', ''),
  (382, 17, '2023-11-09', '07:17:01', '00:00:00', 'absen-in-17-1699489021.jpg', '', 1, '-4.8401176,104.8865534', '', ''),
  (383, 29, '2023-11-09', '07:18:29', '00:00:00', 'absen-in-29-1699489109.jpg', '', 1, '-4.840051,104.8866038', '', ''),
  (384, 19, '2023-11-09', '07:20:38', '00:00:00', 'absen-in-19-1699489238.jpg', '', 1, '-4.84614687962478,104.90739338982155', '', ''),
  (385, 18, '2023-11-09', '07:28:58', '00:00:00', 'absen-in-18-1699489738.jpg', '', 1, '-4.8462107,104.9076678', '', ''),
  (386, 29, '2023-11-09', '00:00:00', '00:00:00', '', '', 3, '', '', ''),
  (387, 30, '2023-11-09', '00:00:00', '00:00:00', '', '', 3, '', '', ''),
  (388, 28, '2023-11-09', '00:00:00', '00:00:00', '', '', 3, '', '', ''),
  (389, 17, '2023-11-10', '07:14:21', '17:12:36', 'absen-in-17-1699575261.jpg', 'absen-out-17-1699611156.jpg', 1, '-4.840205,104.886645', '-4.839884,104.8862872', ''),
  (390, 20, '2023-11-10', '07:15:00', '17:11:16', 'absen-in-20-1699575300.jpg', 'absen-out-20-1699611076.jpg', 1, '-4.8400836,104.8865257', '-4.840106,104.8865645', ''),
  (391, 29, '2023-11-10', '07:15:30', '17:01:04', 'absen-in-29-1699575330.jpg', 'absen-out-29-1699610464.jpg', 1, '-4.8401072,104.8865488', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403494,104.8865008</font></font>', ''),
  (392, 30, '2023-11-10', '07:19:32', '17:00:35', 'absen-in-30-1699575572.jpg', 'absen-out-30-1699610435.jpg', 1, '-4.8400371,104.8863837', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8402586,104.8864494</font></font>', ''),
  (393, 19, '2023-11-10', '07:21:58', '17:07:28', 'absen-in-19-1699575718.jpg', 'absen-out-19-1699610848.jpg', 1, '-4.846352788791522,104.90767305625101', '-4.84628225113934,104.90763926760575', ''),
  (394, 28, '2023-11-10', '07:24:09', '17:00:47', 'absen-in-28-1699575849.jpg', 'absen-out-28-1699610447.jpg', 1, '-4.8399888,104.8863001', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401244,104.8865756</font></font>', ''),
  (395, 18, '2023-11-10', '07:28:37', '17:06:02', 'absen-in-18-1699576117.jpg', 'absen-out-18-1699610762.jpg', 1, '-4.8462089,104.9076699', '-4.8462114,104.9076696', ''),
  (396, 20, '2023-11-11', '07:10:10', '17:02:33', 'absen-in-20-1699661410.jpg', 'absen-out-20-1699696953.jpg', 1, '-4.8400539,104.8863786', '-4.8399942,104.8864327', ''),
  (397, 29, '2023-11-11', '07:12:14', '17:00:25', 'absen-in-29-1699661534.jpg', 'absen-out-29-1699696825.jpg', 1, '-4.8400021,104.8863535', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.84013,104.8865739</font></font>', ''),
  (398, 30, '2023-11-11', '07:13:47', '17:00:59', 'absen-in-30-1699661627.jpg', 'absen-out-30-1699696859.jpg', 1, '-4.8399898,104.8863562', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840132,104.8865728</font></font>', ''),
  (399, 28, '2023-11-11', '07:14:07', '17:00:35', 'absen-in-28-1699661647.jpg', 'absen-out-28-1699696835.jpg', 1, '-4.8400879,104.8865165', '-4.8401423,104.8865688', ''),
  (400, 17, '2023-11-11', '07:16:12', '17:08:47', 'absen-in-17-1699661772.jpg', 'absen-out-17-1699697327.jpg', 1, '-4.8400696,104.8865036', '-4.8401037,104.8865537', ''),
  (401, 19, '2023-11-11', '07:23:15', '17:02:26', 'absen-in-19-1699662195.jpg', 'absen-out-19-1699696946.jpg', 1, '-4.846271270837626,104.90755083160501', '-4.8462908751016585,104.90763241238237', ''),
  (402, 18, '2023-11-11', '17:03:41', '17:03:50', 'absen-in-18-1699697021.jpg', 'absen-out-18-1699697030.jpg', 1, '-4.8461591,104.9075805', '-4.8461591,104.9075805', 'Lupa absen pagi'),
  (403, 29, '2023-11-13', '00:00:00', '00:00:00', '', '', 2, '', '', ''),
  (404, 30, '2023-11-13', '07:01:47', '17:02:31', 'absen-in-30-1699833707.jpg', 'absen-out-30-1699869751.jpg', 1, '-4.839971,104.8863606', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403847,104.8865291</font></font>', ''),
  (405, 20, '2023-11-13', '07:14:19', '00:00:00', 'absen-in-20-1699834459.jpg', '', 1, '-4.8400016,104.8863407', '', ''),
  (406, 28, '2023-11-13', '07:14:27', '17:03:02', 'absen-in-28-1699834467.jpg', 'absen-out-28-1699869782.jpg', 1, '-4.8400109,104.88638', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403659,104.8867383</font></font>', ''),
  (407, 17, '2023-11-13', '07:18:33', '17:03:59', 'absen-in-17-1699834713.jpg', 'absen-out-17-1699869839.jpg', 1, '-4.8399739,104.8864296', '-4.8400774,104.8864981', ''),
  (408, 19, '2023-11-13', '07:23:26', '17:09:43', 'absen-in-19-1699835006.jpg', 'absen-out-19-1699870183.jpg', 1, '-4.846256595475225,104.9075418737285', '-4.846166105600408,104.90744492876567', ''),
  (409, 18, '2023-11-13', '07:26:40', '17:05:16', 'absen-in-18-1699835200.jpg', 'absen-out-18-1699869916.jpg', 1, '-4.8462119,104.9076372', '-4.8462138,104.9076706', ''),
  (410, 30, '2023-11-14', '07:09:18', '17:01:05', 'absen-in-30-1699920558.jpg', 'absen-out-30-1699956065.jpg', 1, '-4.8400712,104.8865073', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401138,104.8865738</font></font>', ''),
  (411, 29, '2023-11-14', '07:11:40', '17:00:20', 'absen-in-29-1699920700.jpg', 'absen-out-29-1699956020.jpg', 1, '-4.8400044,104.8863961', '-4.8403061,104.8865384', ''),
  (412, 17, '2023-11-14', '07:12:39', '17:02:22', 'absen-in-17-1699920759.jpg', 'absen-out-17-1699956142.jpg', 1, '-4.8401072,104.8865493', '-4.8401629,104.8865434', ''),
  (413, 20, '2023-11-14', '07:14:03', '17:01:55', 'absen-in-20-1699920843.jpg', 'absen-out-20-1699956115.jpg', 1, '-4.8401551,104.8865677', '-4.8401606,104.8865642', ''),
  (414, 28, '2023-11-14', '07:17:48', '17:01:39', 'absen-in-28-1699921068.jpg', 'absen-out-28-1699956099.jpg', 1, '-4.8400712,104.8865073', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401138,104.8865738</font></font>', ''),
  (415, 19, '2023-11-14', '07:19:24', '17:05:50', 'absen-in-19-1699921164.jpg', 'absen-out-19-1699956350.jpg', 1, '-4.846266510011981,104.90755010479623', '-4.846290733778428,104.90766878396916', ''),
  (416, 18, '2023-11-14', '17:11:43', '17:11:52', 'absen-in-18-1699956703.jpg', 'absen-out-18-1699956712.jpg', 1, '-4.8462253,104.9076692', '-4.8462253,104.9076692', ''),
  (417, 30, '2023-11-15', '06:54:54', '17:01:24', 'absen-in-30-1700006094.jpg', 'absen-out-30-1700042484.jpg', 1, '-4.8399717,104.8863378', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401113,104.8865761</font></font>', ''),
  (418, 29, '2023-11-15', '07:07:05', '17:01:07', 'absen-in-29-1700006825.jpg', 'absen-out-29-1700042467.jpg', 1, '-4.8400928,104.8865493', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8401206,104.8865711</font></font>', ''),
  (419, 28, '2023-11-15', '07:17:57', '17:02:00', 'absen-in-28-1700007477.jpg', 'absen-out-28-1700042520.jpg', 1, '-4.8400631,104.8865361', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403844,104.8865242</font></font>', ''),
  (420, 17, '2023-11-15', '07:18:08', '17:03:15', 'absen-in-17-1700007488.jpg', 'absen-out-17-1700042595.jpg', 1, '-4.8397833,104.8868752', '-4.8401122,104.8865731', ''),
  (421, 19, '2023-11-15', '07:19:57', '17:07:12', 'absen-in-19-1700007597.jpg', 'absen-out-19-1700042833.jpg', 1, '-4.846325844296294,104.90772170531422', '-4.846133127703113,104.90742502360695', ''),
  (422, 18, '2023-11-15', '17:01:59', '17:05:50', 'absen-in-18-1700042519.jpg', 'absen-out-18-1700042750.jpg', 1, '-4.8462008,104.9076678', '-4.8462091,104.9076651', ''),
  (423, 30, '2023-11-16', '07:01:39', '17:00:53', 'absen-in-30-1700092899.jpg', 'absen-out-30-1700128853.jpg', 1, '-4.8399481,104.886227', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403899,104.8865254</font></font>', ''),
  (424, 29, '2023-11-16', '07:06:40', '17:00:57', 'absen-in-29-1700093200.jpg', 'absen-out-29-1700128857.jpg', 1, '-4.8399075,104.8862298', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.840204,104.8865713</font></font>', ''),
  (425, 28, '2023-11-16', '07:08:56', '17:01:23', 'absen-in-28-1700093336.jpg', 'absen-out-28-1700128883.jpg', 1, '-4.8399719,104.8862662', '<font style=\"vertical-align: inherit;\"><font style=\"vertical-align: inherit;\">-4.8403899,104.8865254</font></font>', ''),
  (426, 20, '2023-11-16', '07:13:07', '17:01:01', 'absen-in-20-1700093587.jpg', 'absen-out-20-1700128861.jpg', 1, '-4.8400618,104.8864992', '-4.8401081,104.8865682', ''),
  (427, 17, '2023-11-16', '07:19:05', '17:03:58', 'absen-in-17-1700093945.jpg', 'absen-out-17-1700129038.jpg', 1, '-4.8400589,104.8865066', '-4.8400589,104.8865066', ''),
  (428, 19, '2023-11-16', '07:22:22', '17:07:58', 'absen-in-19-1700094142.jpg', 'absen-out-19-1700129278.jpg', 1, '-4.84624926834293,104.90752186654721', '-4.846318544977628,104.90767792749173', ''),
  (429, 18, '2023-11-16', '11:32:45', '17:06:29', 'absen-in-18-1700109165.jpg', 'absen-out-18-1700129189.jpg', 1, '-4.8462089,104.9076692', '-4.8462151,104.9076721', ''),
  (430, 32, '2023-11-16', '16:07:06', '17:10:09', 'absen-in-32-1700125626.jpg', 'absen-out-32-1700129409.jpg', 1, '-4.846126124906241,104.90768261651638', '-4.846027822141435,104.90769645025144', ''),
  (431, 30, '2023-11-17', '07:05:36', '00:00:00', 'absen-in-30-1700179536.jpg', '', 1, '-4.8399192,104.8862654', '', ''),
  (432, 17, '2023-11-17', '07:12:05', '17:03:40', 'absen-in-17-1700179925.jpg', 'absen-out-17-1700215420.jpg', 1, '-4.8399328,104.8862938', '-4.8398923,104.8862325', ''),
  (433, 20, '2023-11-17', '07:16:20', '17:02:17', 'absen-in-20-1700180180.jpg', 'absen-out-20-1700215337.jpg', 1, '-4.8399948,104.8863521', '-4.8401121,104.8865754', ''),
  (434, 28, '2023-11-17', '07:19:43', '00:00:00', 'absen-in-28-1700180383.jpg', '', 1, '-4.8400469,104.8865003', '', ''),
  (435, 19, '2023-11-17', '07:21:25', '17:00:13', 'absen-in-19-1700180485.jpg', 'absen-out-19-1700215213.jpg', 1, '-4.846314465813175,104.90761979613767', '-4.846278728397904,104.90766755944378', ''),
  (436, 32, '2023-11-17', '07:21:53', '16:59:51', 'absen-in-32-1700180513.jpg', 'absen-out-32-1700215191.jpg', 1, '-4.8462584114134675,104.9076679801365', '-4.846132391019038,104.9076707111535', ''),
  (437, 18, '2023-11-17', '07:26:24', '16:59:30', 'absen-in-18-1700180784.jpg', 'absen-out-18-1700215170.jpg', 1, '-4.846204,104.9076688', '-4.8462148,104.9076726', ''),
  (438, 29, '2023-11-17', '07:42:13', '00:00:00', 'absen-in-29-1700181733.jpg', '', 1, '-4.8401135,104.8865737', '', ''),
  (439, 20, '2023-11-18', '07:12:34', '17:02:28', 'absen-in-20-1700266354.jpg', 'absen-out-20-1700301748.jpg', 1, '-4.8400962,104.8865631', '-4.8401072,104.8865696', ''),
  (440, 19, '2023-11-18', '07:21:26', '17:05:20', 'absen-in-19-1700266886.jpg', 'absen-out-19-1700301920.jpg', 1, '-4.8463150110645214,104.90743644202354', '-4.846207359323483,104.907660481594', ''),
  (441, 17, '2023-11-18', '07:21:31', '17:02:54', 'absen-in-17-1700266891.jpg', 'absen-out-17-1700301774.jpg', 1, '-4.8399771,104.8863133', '-4.8403841,104.8865221', ''),
  (442, 32, '2023-11-18', '07:21:34', '17:04:00', 'absen-in-32-1700266894.jpg', 'absen-out-32-1700301840.jpg', 1, '-4.846246034183778,104.90763543866404', '-4.846300513959989,104.90769291638688', ''),
  (443, 18, '2023-11-18', '07:28:52', '17:04:09', 'absen-in-18-1700267332.jpg', 'absen-out-18-1700301849.jpg', 1, '-4.8462124,104.9076632', '-4.8462119,104.9076726', ''),
  (444, 20, '2023-11-19', '07:48:37', '00:00:00', 'absen-in-20-1700354917.jpg', '', 1, '-4.8399325,104.8862333', '', ''),
  (445, 39, '2025-07-16', '12:47:11', '00:00:00', 'absen-in-39-1752644831.jpg', '', 1, '-6.2557418,106.6044947', '', ''),
  (446, 40, '2025-07-16', '12:47:59', '00:00:00', 'absen-in-40-1752644879.jpg', '', 1, '-6.255820677321032,106.60454759206338', '', '');

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `present_status`
  --

  CREATE TABLE `present_status` (
    `present_id` int(6) NOT NULL,
    `present_name` varchar(30) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `present_status`
  --

  INSERT INTO `present_status` (`present_id`, `present_name`) VALUES
  (1, 'Hadir'),
  (2, 'Sakit'),
  (3, 'Izin'),
  (4, 'Dinas Luar Kota'),
  (5, 'Dinas Dalam Kota');

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `shift`
  --

  CREATE TABLE `shift` (
    `shift_id` int(11) NOT NULL,
    `shift_name` varchar(20) NOT NULL,
    `time_in` time NOT NULL,
    `time_out` time NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `shift`
  --

  INSERT INTO `shift` (`shift_id`, `shift_name`, `time_in`, `time_out`) VALUES
  (1, 'SIFT PAGI', '07:30:00', '17:00:00');

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `sw_site`
  --

  CREATE TABLE `sw_site` (
    `site_id` int(4) NOT NULL,
    `site_url` varchar(100) NOT NULL,
    `site_name` varchar(50) NOT NULL,
    `site_company` varchar(30) NOT NULL,
    `site_manager` varchar(30) NOT NULL,
    `site_director` varchar(30) NOT NULL,
    `site_phone` char(12) NOT NULL,
    `site_address` text NOT NULL,
    `site_description` text NOT NULL,
    `site_logo` varchar(50) NOT NULL,
    `site_email` varchar(30) NOT NULL,
    `site_email_domain` varchar(50) NOT NULL,
    `gmail_host` varchar(50) NOT NULL,
    `gmail_username` varchar(50) NOT NULL,
    `gmail_password` varchar(50) NOT NULL,
    `gmail_port` varchar(10) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `sw_site`
  --

  INSERT INTO `sw_site` (`site_id`, `site_url`, `site_name`, `site_company`, `site_manager`, `site_director`, `site_phone`, `site_address`, `site_description`, `site_logo`, `site_email`, `site_email_domain`, `gmail_host`, `gmail_username`, `gmail_password`, `gmail_port`) VALUES
  (1, 'www.mitragarda.id', 'SPM #SIAGA', 'SATRIA PRAWIRA MAHARDIKA', 'NANDA RAMDHANA', 'NANDA RAMDHANA', '085222277227', 'Tangerang', 'SPM #SIAGA', 'spm_siagapng.png', '<EMAIL>', '<EMAIL>', 'eydcom.gmail.com', '<EMAIL>', 'eydcom.com', '465');

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `tbl_lokasi`
  --

  CREATE TABLE `tbl_lokasi` (
    `id_lokasi` int(11) NOT NULL,
    `nama_lokasi` varchar(255) NOT NULL,
    `deskripsi_lokasi` text DEFAULT NULL,
    `koordinat_gps` varchar(255) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `tbl_lokasi`
  --

  INSERT INTO `tbl_lokasi` (`id_lokasi`, `nama_lokasi`, `deskripsi_lokasi`, `koordinat_gps`) VALUES
  (1, 'titik 1', 'cek titik 1', '-6.9232929166853525,107.60662078857422');

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `tbl_checklist`
  --

  CREATE TABLE `tbl_checklist` (
    `id_checklist` int(11) NOT NULL,
    `nama_pekerjaan` varchar(255) NOT NULL,
    `deskripsi_pekerjaan` text NOT NULL,
    `kategori_pekerjaan` varchar(100) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `tbl_checklist`
  --

  INSERT INTO `tbl_checklist` (`id_checklist`, `nama_pekerjaan`, `deskripsi_pekerjaan`, `kategori_pekerjaan`) VALUES
  (1, 'Pemeriksaan Keamanan', 'Melakukan pemeriksaan keamanan area', 'Keamanan'),
  (2, 'Pemeriksaan Kebersihan', 'Melakukan pemeriksaan kebersihan area', 'Kebersihan'),
  (3, 'Pemeriksaan Fasilitas', 'Melakukan pemeriksaan kondisi fasilitas', 'Fasilitas');

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `tbl_patroli`
  --

  CREATE TABLE `tbl_patroli` (
    `id_patroli` int(11) NOT NULL,
    `dokumentasi` varchar(255) DEFAULT NULL,
    `tanggal` timestamp NOT NULL DEFAULT current_timestamp(),
    `status` varchar(50) NOT NULL,
    `komentar` text DEFAULT NULL,
    `rating` int(1) NOT NULL DEFAULT 1,
    `id_karyawan` int(11) NOT NULL,
    `id_lokasi` int(11) NOT NULL,
    `id_ceklis` int(11) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `tbl_patroli`
  --

  INSERT INTO `tbl_patroli` (`id_patroli`, `dokumentasi`, `tanggal`, `status`, `komentar`, `rating`, `id_karyawan`, `id_lokasi`, `id_ceklis`) VALUES
  (1, NULL, '2025-07-21 08:00:00', 'Selesai', 'Patroli pagi berjalan normal, semua area aman', 5, 36, 1, 1),
  (2, NULL, '2025-07-21 14:00:00', 'Selesai', 'Pemeriksaan kebersihan area kantor sudah baik', 4, 37, 1, 2),
  (3, NULL, '2025-07-21 16:30:00', 'Dalam Proses', 'Sedang melakukan pemeriksaan fasilitas', 3, 38, 1, 3),
  (4, NULL, '2025-07-21 20:00:00', 'Selesai', 'Patroli malam, semua pintu dan jendela sudah terkunci', 5, 39, 1, 1);

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `user`
  --

  CREATE TABLE `user` (
    `user_id` int(11) NOT NULL,
    `username` varchar(40) NOT NULL,
    `email` varchar(50) NOT NULL,
    `password` varchar(100) NOT NULL,
    `fullname` varchar(40) NOT NULL,
    `registered` datetime NOT NULL,
    `created_login` datetime NOT NULL,
    `last_login` datetime NOT NULL,
    `session` varchar(100) NOT NULL,
    `ip` varchar(20) NOT NULL,
    `browser` varchar(30) NOT NULL,
    `level` int(11) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `user`
  --

  INSERT INTO `user` (`user_id`, `username`, `email`, `password`, `fullname`, `registered`, `created_login`, `last_login`, `session`, `ip`, `browser`, `level`) VALUES
  (1, 'eydcom.com', '<EMAIL>', '36b491d1a1c41097f00a4148d39bed1d079190a4d0750b1b084c7459519e5546', 'SATPAM SIAGA', '2025-07-07 18:46:03', '2025-07-19 19:43:27', '2025-07-17 19:07:36', '-', '127.0.0.1', 'Chrome', 1),
  (2, 'operator', '<EMAIL>', '36b491d1a1c41097f00a4148d39bed1d079190a4d0750b1b084c7459519e5546', 'Operator', '2025-07-07 18:46:03', '2025-07-19 19:43:27', '2025-07-07 18:46:03', '-', '127.0.0.1', 'Chrome', 2),
  (3, 'Nanda', '<EMAIL>', 'ce084e148fc5ec82295e03c29960cb7c15e54e792b3190db6480b37e7a9c1063', 'Nanda', '2025-07-16 12:50:24', '2025-07-17 22:38:22', '2025-07-16 12:58:34', '-', '127.0.0.1', 'Chrome', 1);

  -- --------------------------------------------------------

  --
  -- Struktur dari tabel `user_level`
  --

  CREATE TABLE `user_level` (
    `level_id` int(4) NOT NULL,
    `level_name` varchar(20) NOT NULL
  ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

  --
  -- Dumping data untuk tabel `user_level`
  --

  INSERT INTO `user_level` (`level_id`, `level_name`) VALUES
  (1, 'Administrator'),
  (2, 'Operator');

  --
  -- Indexes for dumped tables
  --

  --
  -- Indeks untuk tabel `building`
  --
  ALTER TABLE `building`
    ADD PRIMARY KEY (`building_id`);

  --
  -- Indeks untuk tabel `cuty`
  --
  ALTER TABLE `cuty`
    ADD PRIMARY KEY (`cuty_id`);

  --
  -- Indeks untuk tabel `employees`
  --
  ALTER TABLE `employees`
    ADD PRIMARY KEY (`id`);

  --
  -- Indeks untuk tabel `holiday`
  --
  ALTER TABLE `holiday`
    ADD PRIMARY KEY (`holiday_id`);

  --
  -- Indeks untuk tabel `permission`
  --
  ALTER TABLE `permission`
    ADD PRIMARY KEY (`permission_id`);

  --
  -- Indeks untuk tabel `position`
  --
  ALTER TABLE `position`
    ADD PRIMARY KEY (`position_id`);

  --
  -- Indeks untuk tabel `presence`
  --
  ALTER TABLE `presence`
    ADD PRIMARY KEY (`presence_id`);

  --
  -- Indeks untuk tabel `present_status`
  --
  ALTER TABLE `present_status`
    ADD PRIMARY KEY (`present_id`);

  --
  -- Indeks untuk tabel `shift`
  --
  ALTER TABLE `shift`
    ADD PRIMARY KEY (`shift_id`);

  --
  -- Indeks untuk tabel `sw_site`
  --
  ALTER TABLE `sw_site`
    ADD PRIMARY KEY (`site_id`);

  --
  -- Indeks untuk tabel `tbl_checklist`
  --
  ALTER TABLE `tbl_checklist`
    ADD PRIMARY KEY (`id_checklist`);

  --
  -- Indeks untuk tabel `tbl_lokasi`
  --
  ALTER TABLE `tbl_lokasi`
    ADD PRIMARY KEY (`id_lokasi`);

  --
  -- Indeks untuk tabel `tbl_patroli`
  --
  ALTER TABLE `tbl_patroli`
    ADD PRIMARY KEY (`id_patroli`),
    ADD KEY `fk_patroli_karyawan` (`id_karyawan`),
    ADD KEY `fk_patroli_lokasi` (`id_lokasi`),
    ADD KEY `fk_patroli_checklist` (`id_ceklis`);

  --
  -- Indeks untuk tabel `user`
  --
  ALTER TABLE `user`
    ADD PRIMARY KEY (`user_id`);

  --
  -- Indeks untuk tabel `user_level`
  --
  ALTER TABLE `user_level`
    ADD PRIMARY KEY (`level_id`);

  --
  -- AUTO_INCREMENT untuk tabel yang dibuang
  --

  --
  -- AUTO_INCREMENT untuk tabel `building`
  --
  ALTER TABLE `building`
    MODIFY `building_id` int(8) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

  --
  -- AUTO_INCREMENT untuk tabel `cuty`
  --
  ALTER TABLE `cuty`
    MODIFY `cuty_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

  --
  -- AUTO_INCREMENT untuk tabel `employees`
  --
  ALTER TABLE `employees`
    MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=42;

  --
  -- AUTO_INCREMENT untuk tabel `holiday`
  --
  ALTER TABLE `holiday`
    MODIFY `holiday_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

  --
  -- AUTO_INCREMENT untuk tabel `permission`
  --
  ALTER TABLE `permission`
    MODIFY `permission_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=34;

  --
  -- AUTO_INCREMENT untuk tabel `position`
  --
  ALTER TABLE `position`
    MODIFY `position_id` int(5) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

  --
  -- AUTO_INCREMENT untuk tabel `presence`
  --
  ALTER TABLE `presence`
    MODIFY `presence_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=447;

  --
  -- AUTO_INCREMENT untuk tabel `present_status`
  --
  ALTER TABLE `present_status`
    MODIFY `present_id` int(6) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

  --
  -- AUTO_INCREMENT untuk tabel `shift`
  --
  ALTER TABLE `shift`
    MODIFY `shift_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

  --
  -- AUTO_INCREMENT untuk tabel `sw_site`
  --
  ALTER TABLE `sw_site`
    MODIFY `site_id` int(4) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

  --
  -- AUTO_INCREMENT untuk tabel `tbl_checklist`
  --
  ALTER TABLE `tbl_checklist`
    MODIFY `id_checklist` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

  --
  -- AUTO_INCREMENT untuk tabel `tbl_lokasi`
  --
  ALTER TABLE `tbl_lokasi`
    MODIFY `id_lokasi` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

  --
  -- AUTO_INCREMENT untuk tabel `tbl_patroli`
  --
  ALTER TABLE `tbl_patroli`
    MODIFY `id_patroli` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

  --
  -- AUTO_INCREMENT untuk tabel `user`
  --
  ALTER TABLE `user`
    MODIFY `user_id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

  --
  -- AUTO_INCREMENT untuk tabel `user_level`
  --
  ALTER TABLE `user_level`
    MODIFY `level_id` int(4) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

  --
  -- Ketidakleluasaan untuk tabel `tbl_patroli`
  --
  ALTER TABLE `tbl_patroli`
    ADD CONSTRAINT `fk_patroli_karyawan` FOREIGN KEY (`id_karyawan`) REFERENCES `employees` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `fk_patroli_lokasi` FOREIGN KEY (`id_lokasi`) REFERENCES `tbl_lokasi` (`id_lokasi`) ON DELETE CASCADE ON UPDATE CASCADE,
    ADD CONSTRAINT `fk_patroli_checklist` FOREIGN KEY (`id_ceklis`) REFERENCES `tbl_checklist` (`id_checklist`) ON DELETE CASCADE ON UPDATE CASCADE;

  COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
