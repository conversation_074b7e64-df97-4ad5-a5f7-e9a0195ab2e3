<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once '../../../sw-library/sw-config.php';
require_once '../../login/login_session.php';
include('../../../sw-library/sw-function.php');

echo "<h2>🔍 Test Tampilan Data Patroli</h2>";
echo "<p>Halaman ini akan menguji apakah data patroli dari database bisa ditampilkan dengan benar</p>";

// Test 1: Cek data patroli mentah
echo "<h3>1. 📊 Data Patroli Mentah</h3>";
$query_raw = "SELECT * FROM tbl_patroli ORDER BY tanggal DESC";
$result_raw = $connection->query($query_raw);

if($result_raw && $result_raw->num_rows > 0) {
    echo "<p>✅ Ditemukan " . $result_raw->num_rows . " data patroli</p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>ID</th><th>Tanggal</th><th>Status</th><th>Rating</th><th>ID Karyawan</th><th>ID Lokasi</th><th>ID Checklist</th><th>Komentar</th>";
    echo "</tr>";
    
    while($row = $result_raw->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id_patroli'] . "</td>";
        echo "<td>" . $row['tanggal'] . "</td>";
        echo "<td>" . $row['status'] . "</td>";
        echo "<td>" . $row['rating'] . "</td>";
        echo "<td>" . $row['id_karyawan'] . "</td>";
        echo "<td>" . $row['id_lokasi'] . "</td>";
        echo "<td>" . $row['id_ceklis'] . "</td>";
        echo "<td>" . substr($row['komentar'], 0, 50) . "...</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ Tidak ada data patroli ditemukan</p>";
}

// Test 2: Cek data dengan JOIN (seperti yang digunakan DataTable)
echo "<h3>2. 🔗 Data Patroli dengan JOIN</h3>";
$query_join = "SELECT 
                p.id_patroli,
                p.tanggal,
                e.employees_name,
                l.nama_lokasi,
                c.nama_pekerjaan,
                p.status,
                p.rating,
                p.dokumentasi,
                p.komentar
               FROM tbl_patroli p 
               LEFT JOIN employees e ON p.id_karyawan = e.id 
               LEFT JOIN tbl_lokasi l ON p.id_lokasi = l.id_lokasi 
               LEFT JOIN tbl_checklist c ON p.id_ceklis = c.id_checklist
               ORDER BY p.tanggal DESC";

$result_join = $connection->query($query_join);

if($result_join && $result_join->num_rows > 0) {
    echo "<p>✅ Query JOIN berhasil, ditemukan " . $result_join->num_rows . " data</p>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>ID</th><th>Tanggal</th><th>Karyawan</th><th>Lokasi</th><th>Checklist</th><th>Status</th><th>Rating</th><th>Komentar</th>";
    echo "</tr>";
    
    while($row = $result_join->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id_patroli'] . "</td>";
        echo "<td>" . tgl_indo($row['tanggal']) . ' ' . jam_indo($row['tanggal']) . "</td>";
        echo "<td>" . ($row['employees_name'] ?: '<span style="color:red;">NULL</span>') . "</td>";
        echo "<td>" . ($row['nama_lokasi'] ?: '<span style="color:red;">NULL</span>') . "</td>";
        echo "<td>" . ($row['nama_pekerjaan'] ?: '<span style="color:red;">NULL</span>') . "</td>";
        echo "<td><span style='background: #28a745; color: white; padding: 2px 6px; border-radius: 3px;'>" . $row['status'] . "</span></td>";
        
        // Rating stars
        $stars = '';
        for($i = 1; $i <= 5; $i++){
            if($i <= $row['rating']){
                $stars .= '⭐';
            } else {
                $stars .= '☆';
            }
        }
        echo "<td>" . $stars . " (" . $row['rating'] . ")</td>";
        echo "<td>" . substr($row['komentar'], 0, 30) . "...</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>❌ Query JOIN gagal atau tidak ada data</p>";
    if($connection->error) {
        echo "<p style='color: red;'>Error: " . $connection->error . "</p>";
    }
}

// Test 3: Simulasi DataTable Response
echo "<h3>3. 📋 Simulasi Response DataTable</h3>";

// Simulate DataTable parameters
$aColumns = [
    'id_patroli',
    'tanggal', 
    'employees_name',
    'nama_lokasi',
    'nama_pekerjaan',
    'status',
    'rating',
    'dokumentasi'
];

$sTable = "tbl_patroli p 
           LEFT JOIN employees e ON p.id_karyawan = e.id 
           LEFT JOIN tbl_lokasi l ON p.id_lokasi = l.id_lokasi 
           LEFT JOIN tbl_checklist c ON p.id_ceklis = c.id_checklist";

$sQuery = "SELECT 
           p.id_patroli,
           p.tanggal,
           e.employees_name,
           l.nama_lokasi,
           c.nama_pekerjaan,
           p.status,
           p.rating,
           p.dokumentasi,
           p.komentar
           FROM " . $sTable . " 
           ORDER BY p.tanggal DESC 
           LIMIT 10";

$rResult = $connection->query($sQuery);

if($rResult) {
    echo "<p>✅ Query DataTable berhasil</p>";
    echo "<p><strong>Query:</strong> <code>" . htmlspecialchars($sQuery) . "</code></p>";
    
    if($rResult->num_rows > 0) {
        echo "<h4>Data yang akan ditampilkan di DataTable:</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>No</th><th>Tanggal</th><th>Karyawan</th><th>Lokasi</th><th>Checklist</th><th>Status</th><th>Rating</th><th>Dokumentasi</th><th>Aksi</th>";
        echo "</tr>";
        
        $no = 1;
        while($aRow = $rResult->fetch_array()) {
            echo "<tr>";
            
            // No
            echo "<td>" . $no++ . "</td>";
            
            // Tanggal
            echo "<td>" . tgl_indo($aRow['tanggal']) . ' ' . jam_indo($aRow['tanggal']) . "</td>";
            
            // Karyawan
            echo "<td>" . ($aRow['employees_name'] ?: '<span style="color:red;">NULL</span>') . "</td>";
            
            // Lokasi
            echo "<td>" . ($aRow['nama_lokasi'] ?: '<span style="color:red;">NULL</span>') . "</td>";
            
            // Checklist
            echo "<td>" . ($aRow['nama_pekerjaan'] ?: '<span style="color:red;">NULL</span>') . "</td>";
            
            // Status
            $status_class = '';
            switch($aRow['status']){
                case 'Selesai':
                    $status_class = 'background: #28a745; color: white;';
                    break;
                case 'Dalam Proses':
                    $status_class = 'background: #ffc107; color: black;';
                    break;
                default:
                    $status_class = 'background: #6c757d; color: white;';
            }
            echo "<td><span style='$status_class padding: 2px 6px; border-radius: 3px;'>" . $aRow['status'] . "</span></td>";
            
            // Rating
            $stars = '';
            for($i = 1; $i <= 5; $i++){
                if($i <= $aRow['rating']){
                    $stars .= '⭐';
                } else {
                    $stars .= '☆';
                }
            }
            echo "<td>" . $stars . "</td>";
            
            // Dokumentasi
            if(!empty($aRow['dokumentasi'])){
                echo "<td>📷 Ada</td>";
            } else {
                echo "<td>-</td>";
            }
            
            // Aksi
            echo "<td>";
            echo "<button style='margin: 2px; padding: 4px 8px; background: #17a2b8; color: white; border: none; border-radius: 3px;'>👁️ Detail</button>";
            echo "<button style='margin: 2px; padding: 4px 8px; background: #ffc107; color: black; border: none; border-radius: 3px;'>✏️ Edit</button>";
            echo "<button style='margin: 2px; padding: 4px 8px; background: #dc3545; color: white; border: none; border-radius: 3px;'>🗑️ Hapus</button>";
            echo "</td>";
            
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>⚠️ Query berhasil tapi tidak ada data yang dikembalikan</p>";
    }
} else {
    echo "<p>❌ Query DataTable gagal</p>";
    echo "<p style='color: red;'>Error: " . $connection->error . "</p>";
}

// Test 4: Cek tabel terkait
echo "<h3>4. 🔍 Cek Tabel Terkait</h3>";

$tables_to_check = [
    'employees' => 'SELECT COUNT(*) as total, MIN(id) as min_id, MAX(id) as max_id FROM employees',
    'tbl_lokasi' => 'SELECT COUNT(*) as total, MIN(id_lokasi) as min_id, MAX(id_lokasi) as max_id FROM tbl_lokasi',
    'tbl_checklist' => 'SELECT COUNT(*) as total, MIN(id_checklist) as min_id, MAX(id_checklist) as max_id FROM tbl_checklist'
];

foreach($tables_to_check as $table => $query) {
    $result = $connection->query($query);
    if($result) {
        $row = $result->fetch_assoc();
        echo "<p><strong>$table:</strong> " . $row['total'] . " records (ID: " . $row['min_id'] . " - " . $row['max_id'] . ")</p>";
    } else {
        echo "<p><strong>$table:</strong> Error - " . $connection->error . "</p>";
    }
}

echo "<br><br>";
echo "<div style='background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px;'>";
echo "<h4>💡 Kesimpulan:</h4>";
echo "<p>Jika semua test di atas menunjukkan ✅ dan data terlihat, maka masalahnya kemungkinan ada di:</p>";
echo "<ul>";
echo "<li>JavaScript DataTable tidak ter-load dengan benar</li>";
echo "<li>File sw-datatable.php tidak dapat diakses</li>";
echo "<li>Ada error di console browser</li>";
echo "<li>Session atau permission user</li>";
echo "</ul>";
echo "<p><strong>Langkah selanjutnya:</strong> Buka halaman patroli dan periksa console browser (F12) untuk melihat error JavaScript.</p>";
echo "</div>";

echo "<br><br>";
echo "<a href='../../../patroli' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>← Kembali ke Data Patroli</a> ";
echo "<a href='debug_datatable.php' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>Debug DataTable →</a>";
?>
