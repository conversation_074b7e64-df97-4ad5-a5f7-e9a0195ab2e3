<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once '../../../sw-library/sw-config.php';
require_once '../../login/login_session.php';
include('../../../sw-library/sw-function.php');
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Simple DataTable Patroli</title>
    <meta charset="utf-8">
    <link rel="stylesheet" href="../../sw-assets/css/bootstrap.min.css">
    <link rel="stylesheet" href="../../sw-assets/plugins/datatables/dataTables.bootstrap.css">
    <link rel="stylesheet" href="../../sw-assets/css/font-awesome.min.css">
    <style>
        body { padding: 20px; }
        .test-info { background: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1>🧪 Test Simple DataTable Patroli</h1>
        
        <div class="test-info">
            <h4>📋 Informasi Test</h4>
            <p>Halaman ini menggunakan DataTable dengan syntax yang sama persis dengan modul karyawan/absensi.</p>
            <p><strong>User Level:</strong> <?php echo $level_user; ?></p>
            <p><strong>Session User:</strong> <?php echo $_SESSION['SESSION_USER']; ?></p>
        </div>
        
        <div class="row">
            <div class="col-xs-12">
                <div class="box box-solid">
                    <div class="box-header with-border">
                        <h3 class="box-title">Data Patroli</h3>
                        <div class="box-tools pull-right">
                            <button type="button" class="btn btn-info btn-sm" onclick="reloadTable()">
                                <i class="fa fa-refresh"></i> Refresh
                            </button>
                        </div>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="sw-datatable" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th style="width: 10px">No</th>
                                        <th>Tanggal</th>
                                        <th>Karyawan</th>
                                        <th>Lokasi</th>
                                        <th>Checklist</th>
                                        <th>Status</th>
                                        <th>Rating</th>
                                        <th>Dokumentasi</th>
                                        <th style="width:150px" class="text-center">Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-info">
            <h4>🔗 Navigation</h4>
            <a href="../../../patroli" class="btn btn-primary">← Kembali ke Data Patroli</a>
            <a href="test_display.php" class="btn btn-info">Test Display →</a>
            <a href="check_setup.php" class="btn btn-warning">Check Setup →</a>
        </div>
    </div>

    <!-- Scripts -->
    <script src="../../sw-assets/js/jquery.min.js"></script>
    <script src="../../sw-assets/js/bootstrap.min.js"></script>
    <script src="../../sw-assets/plugins/datatables/jquery.dataTables.min.js"></script>
    <script src="../../sw-assets/plugins/datatables/dataTables.bootstrap.min.js"></script>
    
    <script type="text/javascript">
    $(document).ready(function() {
        console.log('Initializing DataTable...');
        
        // Initialize DataTable dengan syntax yang sama dengan modul lain
        function loadDataPatroli() {
            $('#sw-datatable').dataTable({
                "bProcessing": true,
                "bServerSide": true,
                "bAutoWidth": true,
                "bSort": true,
                "bStateSave": true,
                "bDestroy": true,
                "aaSorting": [[1, 'desc']], // Sort by tanggal descending
                "iDisplayLength": 25,
                "aLengthMenu": [
                    [10, 25, 50, 100],
                    [10, 25, 50, 100]
                ],
                "sAjaxSource": "sw-datatable.php",
                "aoColumns": [
                    null, // No
                    null, // Tanggal
                    null, // Karyawan
                    null, // Lokasi
                    null, // Checklist
                    null, // Status
                    null, // Rating
                    null, // Dokumentasi
                    null  // Aksi
                ],
                "oLanguage": {
                    "sProcessing": "Memproses...",
                    "sLengthMenu": "Tampilkan _MENU_ data per halaman",
                    "sZeroRecords": "Data tidak ditemukan",
                    "sInfo": "Menampilkan _START_ sampai _END_ dari _TOTAL_ data",
                    "sInfoEmpty": "Menampilkan 0 sampai 0 dari 0 data",
                    "sInfoFiltered": "(disaring dari _MAX_ total data)",
                    "sSearch": "Cari:",
                    "oPaginate": {
                        "sFirst": "Pertama",
                        "sLast": "Terakhir",
                        "sNext": "Selanjutnya",
                        "sPrevious": "Sebelumnya"
                    }
                },
                "fnDrawCallback": function(oSettings) {
                    console.log('DataTable redrawn with', oSettings.fnRecordsTotal(), 'total records');
                    
                    // Show result info
                    var info = 'DataTable berhasil dimuat dengan ' + oSettings.fnRecordsTotal() + ' total records';
                    if(oSettings.fnRecordsTotal() > 0) {
                        $('.test-info:last').after('<div class="alert alert-success">' + info + '</div>');
                    } else {
                        $('.test-info:last').after('<div class="alert alert-warning">' + info + ' (Data kosong)</div>');
                    }
                },
                "fnServerData": function(sSource, aoData, fnCallback) {
                    console.log('Making AJAX request to:', sSource);
                    console.log('With data:', aoData);
                    
                    $.ajax({
                        "dataType": 'json',
                        "type": "GET",
                        "url": sSource,
                        "data": aoData,
                        "success": function(json) {
                            console.log('DataTable response received:', json);
                            fnCallback(json);
                        },
                        "error": function(xhr, error, thrown) {
                            console.error('DataTable AJAX Error:', error);
                            console.error('Response Text:', xhr.responseText);
                            console.error('Status:', xhr.status);
                            
                            $('.test-info:last').after('<div class="alert alert-danger"><strong>Error:</strong> ' + error + '<br><strong>Response:</strong> ' + xhr.responseText + '</div>');
                        }
                    });
                }
            });
        }

        // Load data saat halaman dimuat
        loadDataPatroli();

        // Function to reload DataTable
        window.reloadTable = function() {
            console.log('Reloading table...');
            $('.alert').remove(); // Remove previous alerts
            $('#sw-datatable').dataTable().fnReloadAjax();
        };
    });
    </script>
</body>
</html>
