<?php
session_start();
if(empty($_SESSION['SESSION_USER']) && empty($_SESSION['SESSION_ID'])){
    header('location:../../login/');
    exit;
}

require_once '../../../sw-library/sw-config.php';
require_once '../../login/login_session.php';
include('../../../sw-library/sw-function.php');

$connection = mysqli_connect(DB_HOST, DB_USER, DB_PASSWD, DB_NAME) or die(mysqli_error($connection));

switch(@$_GET['action']){
    
    // ========== TAMBAH PATROLI ==========
    case 'add':
        $error = array();
        
        // Validasi input
        if(empty($_POST['id_karyawan'])) {
            $error[] = 'Karyawan harus dipilih';
        }
        if(empty($_POST['id_lokasi'])) {
            $error[] = 'Lokasi harus dipilih';
        }
        if(empty($_POST['id_ceklis'])) {
            $error[] = 'Checklist harus dipilih';
        }
        if(empty($_POST['status'])) {
            $error[] = 'Status harus dipilih';
        }
        if(empty($_POST['rating'])) {
            $error[] = 'Rating harus dipilih';
        }
        
        $id_karyawan = anti_injection($_POST['id_karyawan']);
        $id_lokasi = anti_injection($_POST['id_lokasi']);
        $id_ceklis = anti_injection($_POST['id_ceklis']);
        $status = anti_injection($_POST['status']);
        $rating = anti_injection($_POST['rating']);
        $komentar = anti_injection($_POST['komentar']);
        
        // Handle upload dokumentasi
        $dokumentasi = '';
        if(!empty($_FILES['dokumentasi']['name'])){
            $target_dir = "../../../sw-content/patroli/";
            
            // Buat direktori jika belum ada
            if (!file_exists($target_dir)) {
                mkdir($target_dir, 0777, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['dokumentasi']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = array('jpg', 'jpeg', 'png');
            
            if(!in_array($file_extension, $allowed_extensions)){
                $error[] = 'Format file tidak diizinkan. Gunakan JPG, JPEG, atau PNG';
            }
            
            if($_FILES['dokumentasi']['size'] > 2097152){ // 2MB
                $error[] = 'Ukuran file terlalu besar. Maksimal 2MB';
            }
            
            if(empty($error)){
                $dokumentasi = date('YmdHis') . '_' . uniqid() . '.' . $file_extension;
                $target_file = $target_dir . $dokumentasi;
                
                if(!move_uploaded_file($_FILES['dokumentasi']['tmp_name'], $target_file)){
                    $error[] = 'Gagal mengupload file dokumentasi';
                }
            }
        }
        
        if(empty($error)){
            $add = "INSERT INTO tbl_patroli (
                        id_karyawan, 
                        id_lokasi, 
                        id_ceklis, 
                        status, 
                        rating, 
                        komentar, 
                        dokumentasi,
                        tanggal
                    ) VALUES (
                        '$id_karyawan', 
                        '$id_lokasi', 
                        '$id_ceklis', 
                        '$status', 
                        '$rating', 
                        '$komentar', 
                        '$dokumentasi',
                        NOW()
                    )";
            
            if($connection->query($add)){
                header('Location: ../../patroli');
                exit;
            } else {
                echo 'Gagal menambahkan data patroli: ' . $connection->error;
            }
        } else {
            foreach($error as $val){
                echo $val . '<br>';
            }
        }
        break;
    
    // ========== UPDATE PATROLI ==========
    case 'update':
        $error = array();
        
        // Validasi input
        if(empty($_POST['id_patroli'])) {
            $error[] = 'ID Patroli tidak valid';
        }
        if(empty($_POST['id_karyawan'])) {
            $error[] = 'Karyawan harus dipilih';
        }
        if(empty($_POST['id_lokasi'])) {
            $error[] = 'Lokasi harus dipilih';
        }
        if(empty($_POST['id_ceklis'])) {
            $error[] = 'Checklist harus dipilih';
        }
        if(empty($_POST['status'])) {
            $error[] = 'Status harus dipilih';
        }
        if(empty($_POST['rating'])) {
            $error[] = 'Rating harus dipilih';
        }
        
        $id_patroli = anti_injection($_POST['id_patroli']);
        $id_karyawan = anti_injection($_POST['id_karyawan']);
        $id_lokasi = anti_injection($_POST['id_lokasi']);
        $id_ceklis = anti_injection($_POST['id_ceklis']);
        $status = anti_injection($_POST['status']);
        $rating = anti_injection($_POST['rating']);
        $komentar = anti_injection($_POST['komentar']);
        
        // Get current data
        $query_current = "SELECT dokumentasi FROM tbl_patroli WHERE id_patroli = '$id_patroli'";
        $result_current = $connection->query($query_current);
        $current_data = $result_current->fetch_assoc();
        $dokumentasi = $current_data['dokumentasi'];
        
        // Handle upload dokumentasi baru
        if(!empty($_FILES['dokumentasi']['name'])){
            $target_dir = "../../../sw-content/patroli/";
            
            // Buat direktori jika belum ada
            if (!file_exists($target_dir)) {
                mkdir($target_dir, 0777, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['dokumentasi']['name'], PATHINFO_EXTENSION));
            $allowed_extensions = array('jpg', 'jpeg', 'png');
            
            if(!in_array($file_extension, $allowed_extensions)){
                $error[] = 'Format file tidak diizinkan. Gunakan JPG, JPEG, atau PNG';
            }
            
            if($_FILES['dokumentasi']['size'] > 2097152){ // 2MB
                $error[] = 'Ukuran file terlalu besar. Maksimal 2MB';
            }
            
            if(empty($error)){
                // Hapus file lama jika ada
                if(!empty($current_data['dokumentasi']) && file_exists($target_dir . $current_data['dokumentasi'])){
                    unlink($target_dir . $current_data['dokumentasi']);
                }
                
                $dokumentasi = date('YmdHis') . '_' . uniqid() . '.' . $file_extension;
                $target_file = $target_dir . $dokumentasi;
                
                if(!move_uploaded_file($_FILES['dokumentasi']['tmp_name'], $target_file)){
                    $error[] = 'Gagal mengupload file dokumentasi';
                }
            }
        }
        
        if(empty($error)){
            $update = "UPDATE tbl_patroli SET 
                        id_karyawan = '$id_karyawan',
                        id_lokasi = '$id_lokasi',
                        id_ceklis = '$id_ceklis',
                        status = '$status',
                        rating = '$rating',
                        komentar = '$komentar',
                        dokumentasi = '$dokumentasi'
                      WHERE id_patroli = '$id_patroli'";
            
            if($connection->query($update)){
                header('Location: ../../patroli');
                exit;
            } else {
                echo 'Gagal mengupdate data patroli: ' . $connection->error;
            }
        } else {
            foreach($error as $val){
                echo $val . '<br>';
            }
        }
        break;
    
    // ========== HAPUS PATROLI ==========
    case 'delete':
        if(!empty($_POST['id'])){
            $id = anti_injection(epm_decode($_POST['id']));
            
            // Get file dokumentasi untuk dihapus
            $query_file = "SELECT dokumentasi FROM tbl_patroli WHERE id_patroli = '$id'";
            $result_file = $connection->query($query_file);
            if($result_file->num_rows > 0){
                $row_file = $result_file->fetch_assoc();
                if(!empty($row_file['dokumentasi'])){
                    $file_path = "../../../sw-content/patroli/" . $row_file['dokumentasi'];
                    if(file_exists($file_path)){
                        unlink($file_path);
                    }
                }
            }
            
            $deleted = "DELETE FROM tbl_patroli WHERE id_patroli = '$id'";
            if($connection->query($deleted)){
                header('Location: ../../patroli');
                exit;
            } else {
                echo 'Data tidak berhasil dihapus!<br>' . $connection->error;
            }
        } else {
            echo 'ID tidak boleh kosong';
        }
        break;
    
    // ========== DEFAULT ==========
    default:
        echo 'Aksi tidak dikenali.';
        break;
}
?>
